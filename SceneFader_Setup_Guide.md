# SceneFader 场景渐变效果设置指南

## 概述

SceneFader 是一个用于在 Cocos Creator 中实现场景切换渐入渐出效果的组件。它提供了平滑的黑屏过渡效果，让场景切换更加自然。

## 功能特点

- 🎬 平滑的渐入渐出效果
- 🔄 自动管理场景切换
- 🎯 单例模式，确保全局唯一
- 🚀 简单易用的静态方法
- 💾 常驻节点，不会在场景切换时被销毁

## 设置步骤

### 1. 在主场景中添加 SceneFader

1. 在你的主菜单场景（MainMenu）中创建一个空节点
2. 将节点命名为 "SceneFader" 或其他你喜欢的名字
3. 为这个节点添加 `SceneFader` 组件

### 2. 节点层级设置

确保 SceneFader 节点在场景的最顶层，这样渐变遮罩才能覆盖整个屏幕：

```
MainMenu (Scene)
├── UI (Canvas)
│   ├── Background
│   ├── StartButton
│   └── ...
└── SceneFader (Node) ← 添加 SceneFader 组件
```

### 3. 使用方法

在你的脚本中，使用 `SceneFader.loadScene()` 替代 `director.loadScene()`：

```typescript
// 原来的代码
director.loadScene("LevelSelect");

// 改为
SceneFader.loadScene("LevelSelect");
```

## 已修改的文件

以下文件已经更新为使用 SceneFader：

1. **MainMenuController.ts** - 主菜单的单人游戏按钮
2. **SelectManager.ts** - 关卡选择的开始游戏按钮
3. **GameManager.ts** - 游戏中的重新开始和返回主菜单

## 效果说明

- **渐出效果**：当切换场景时，屏幕会逐渐变黑（0.5秒）
- **场景加载**：在黑屏状态下加载新场景
- **渐入效果**：新场景加载完成后，黑屏逐渐消失（0.5秒）

## 自定义选项

你可以通过修改 SceneFader.ts 来自定义效果：

```typescript
// 修改渐变时长（默认0.5秒）
fadeIn(0.8);  // 渐入时长0.8秒
fadeOut(onComplete, 0.8);  // 渐出时长0.8秒

// 修改遮罩颜色（默认黑色）
graphics.fillColor = color(255, 255, 255, 255);  // 白色遮罩
```

## 注意事项

1. **只需要在一个场景中添加**：SceneFader 使用常驻节点，一旦创建就会在所有场景中生效
2. **确保在最顶层**：SceneFader 节点应该在场景的最顶层，确保遮罩能覆盖所有UI
3. **避免重复创建**：SceneFader 使用单例模式，重复创建会自动销毁多余的实例

## 故障排除

### 问题：渐变效果不显示
- 检查 SceneFader 节点是否在场景的最顶层
- 确认节点已添加 SceneFader 组件
- 检查控制台是否有错误信息

### 问题：场景切换没有效果
- 确认代码中使用的是 `SceneFader.loadScene()` 而不是 `director.loadScene()`
- 检查场景名称是否正确

### 问题：黑屏卡住不消失
- 检查目标场景是否能正常加载
- 查看控制台错误信息
- 确认 SceneFader 组件没有被意外销毁

## API 参考

### 静态方法

```typescript
// 使用渐变效果加载场景
SceneFader.loadScene(sceneName: string)
```

### 实例方法

```typescript
// 渐入效果（从黑屏到透明）
fadeIn(duration?: number)

// 渐出效果（从透明到黑屏）
fadeOut(onComplete?: () => void, duration?: number)

// 带渐变效果的场景加载
loadScene(sceneName: string)
```
