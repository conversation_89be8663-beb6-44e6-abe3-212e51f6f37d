import { _decorator, Component, Button, Label } from 'cc';
import { SceneFader } from './SceneFader';
const { ccclass, property } = _decorator;

/**
 * SceneFader 测试组件
 * 用于测试场景渐变效果是否正常工作
 */
@ccclass('SceneFaderTest')
export class SceneFaderTest extends Component {
    @property(Button)
    testButton: Button = null!;

    @property(Label)
    statusLabel: Label = null!;

    private testScenes: string[] = ['mainmenu', 'LevelSelect', 'gamescene'];
    private currentSceneIndex: number = 0;

    start() {
        if (this.testButton) {
            this.testButton.node.on(Button.EventType.CLICK, this.onTestButtonClick, this);
        }
        
        this.updateStatusLabel();
    }

    onTestButtonClick() {
        // 循环切换到下一个场景
        this.currentSceneIndex = (this.currentSceneIndex + 1) % this.testScenes.length;
        const nextScene = this.testScenes[this.currentSceneIndex];
        
        console.log(`Testing SceneFader: Loading scene ${nextScene}`);
        
        // 使用 SceneFader 切换场景
        SceneFader.loadScene(nextScene);
    }

    private updateStatusLabel() {
        if (this.statusLabel) {
            const currentScene = this.testScenes[this.currentSceneIndex];
            this.statusLabel.string = `当前场景: ${currentScene}\n点击按钮测试场景切换`;
        }
    }

    onDestroy() {
        if (this.testButton) {
            this.testButton.node.off(Button.EventType.CLICK, this.onTestButtonClick, this);
        }
    }
}
