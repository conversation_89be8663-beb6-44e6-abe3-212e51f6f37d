import { _decorator, Component, Button } from 'cc';
import { SceneFader } from './SceneFader';
const { ccclass, property } = _decorator;

@ccclass('MainMenuController')
export class MainMenuController extends Component {
    @property(Button)
    startGameBtn: Button = null!; // 拖拽你的"开始游戏"按钮到这里

    start() {
        if (this.startGameBtn) {
            this.startGameBtn.node.on(Button.EventType.CLICK, this.onStartGame, this);
        }
    }

    onStartGame() {
        // 使用渐变效果切换到关卡选择场景
        SceneFader.loadScene("LevelSelect");
        // director.loadScene("gamescene");
    }
} 