{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2015", "module": "ES2015", "strict": true, "types": ["./temp/declarations/cc.custom-macro", "./temp/declarations/cc", "./temp/declarations/jsb", "./temp/declarations/cc.env"], "paths": {"db://internal/*": ["/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/*"], "db://assets/*": ["/Users/<USER>/projects/cocos_project/driftClash/assets/*"]}, "experimentalDecorators": true, "isolatedModules": true, "moduleResolution": "node", "noEmit": true, "forceConsistentCasingInFileNames": true}}