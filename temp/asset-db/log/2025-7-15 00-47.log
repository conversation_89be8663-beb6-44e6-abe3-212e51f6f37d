2025-7-15 00:47:23-debug: start **** info
2025-7-15 00:47:23-log: Cannot access game frame or container.
2025-7-15 00:47:23-debug: asset-db:require-engine-code (359ms)
2025-7-15 00:47:23-log: [box2d]:box2d wasm lib loaded.
2025-7-15 00:47:23-log: meshopt wasm decoder initialized
2025-7-15 00:47:23-log: [bullet]:bullet wasm lib loaded.
2025-7-15 00:47:23-log: Cocos Creator v3.8.6
2025-7-15 00:47:23-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:29.45MB, end 80.00MB, increase: 50.55MB
2025-7-15 00:47:23-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.82MB, end 88.51MB, increase: 7.69MB
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db-plugin-register: builder start:88.53MB, end 229.62MB, increase: 141.08MB
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.71MB, end 227.80MB, increase: 147.09MB
2025-7-15 00:47:23-log: Forward render pipeline initialized.
2025-7-15 00:47:23-log: Using legacy pipeline
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.01MB, end 228.39MB, increase: 148.38MB
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:224.75MB, end 227.64MB, increase: 2.89MB
2025-7-15 00:47:24-debug: run package(google-play) handler(enable) start
2025-7-15 00:47:24-debug: run package(honor-mini-game) handler(enable) start
2025-7-15 00:47:24-debug: run package(honor-mini-game) handler(enable) success!
2025-7-15 00:47:24-debug: run package(huawei-agc) handler(enable) start
2025-7-15 00:47:24-debug: run package(huawei-agc) handler(enable) success!
2025-7-15 00:47:24-debug: run package(huawei-quick-game) handler(enable) start
2025-7-15 00:47:24-debug: run package(huawei-quick-game) handler(enable) success!
2025-7-15 00:47:24-debug: run package(ios) handler(enable) start
2025-7-15 00:47:24-debug: run package(ios) handler(enable) success!
2025-7-15 00:47:24-debug: run package(linux) handler(enable) start
2025-7-15 00:47:24-debug: run package(linux) handler(enable) success!
2025-7-15 00:47:24-debug: run package(mac) handler(enable) start
2025-7-15 00:47:24-debug: run package(mac) handler(enable) success!
2025-7-15 00:47:24-debug: run package(migu-mini-game) handler(enable) start
2025-7-15 00:47:24-debug: run package(migu-mini-game) handler(enable) success!
2025-7-15 00:47:24-debug: run package(native) handler(enable) start
2025-7-15 00:47:24-debug: run package(harmonyos-next) handler(enable) success!
2025-7-15 00:47:24-debug: run package(harmonyos-next) handler(enable) start
2025-7-15 00:47:24-debug: run package(ohos) handler(enable) start
2025-7-15 00:47:24-debug: run package(ohos) handler(enable) success!
2025-7-15 00:47:24-debug: run package(oppo-mini-game) handler(enable) start
2025-7-15 00:47:24-debug: run package(oppo-mini-game) handler(enable) success!
2025-7-15 00:47:24-debug: run package(runtime-dev-tools) handler(enable) start
2025-7-15 00:47:24-debug: run package(google-play) handler(enable) success!
2025-7-15 00:47:24-debug: run package(taobao-mini-game) handler(enable) success!
2025-7-15 00:47:24-debug: run package(vivo-mini-game) handler(enable) start
2025-7-15 00:47:24-debug: run package(vivo-mini-game) handler(enable) success!
2025-7-15 00:47:24-debug: run package(web-desktop) handler(enable) start
2025-7-15 00:47:24-debug: run package(web-desktop) handler(enable) success!
2025-7-15 00:47:24-debug: run package(native) handler(enable) success!
2025-7-15 00:47:24-debug: run package(web-mobile) handler(enable) success!
2025-7-15 00:47:24-debug: run package(wechatgame) handler(enable) start
2025-7-15 00:47:24-debug: run package(wechatgame) handler(enable) success!
2025-7-15 00:47:24-debug: run package(wechatprogram) handler(enable) start
2025-7-15 00:47:24-debug: run package(wechatprogram) handler(enable) success!
2025-7-15 00:47:24-debug: run package(windows) handler(enable) start
2025-7-15 00:47:24-debug: run package(windows) handler(enable) success!
2025-7-15 00:47:24-debug: run package(runtime-dev-tools) handler(enable) success!
2025-7-15 00:47:24-debug: run package(xiaomi-quick-game) handler(enable) start
2025-7-15 00:47:24-debug: run package(cocos-service) handler(enable) start
2025-7-15 00:47:24-debug: run package(cocos-service) handler(enable) success!
2025-7-15 00:47:24-debug: run package(im-plugin) handler(enable) start
2025-7-15 00:47:24-debug: run package(im-plugin) handler(enable) success!
2025-7-15 00:47:24-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-7-15 00:47:24-debug: run package(web-mobile) handler(enable) start
2025-7-15 00:47:24-debug: run package(taobao-mini-game) handler(enable) start
2025-7-15 00:47:24-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-7-15 00:47:24-debug: asset-db:worker-init: initPlugin (772ms)
2025-7-15 00:47:24-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db:worker-init start:29.44MB, end 229.49MB, increase: 200.05MB
2025-7-15 00:47:24-debug: Run asset db hook engine-extends:beforePreStart ...
2025-7-15 00:47:24-debug: Run asset db hook engine-extends:beforePreStart success!
2025-7-15 00:47:24-debug: Run asset db hook programming:beforePreStart ...
2025-7-15 00:47:24-debug: Run asset db hook programming:beforePreStart success!
2025-7-15 00:47:24-debug: run package(placeholder) handler(enable) success!
2025-7-15 00:47:24-debug: run package(placeholder) handler(enable) start
2025-7-15 00:47:24-debug: asset-db-hook-programming-beforePreStart (36ms)
2025-7-15 00:47:24-debug: asset-db-hook-engine-extends-beforePreStart (36ms)
2025-7-15 00:47:24-debug: asset-db:worker-init (1221ms)
2025-7-15 00:47:24-debug: Preimport db internal success
2025-7-15 00:47:24-debug: Preimport db assets success
2025-7-15 00:47:24-debug: Run asset db hook programming:afterPreStart ...
2025-7-15 00:47:24-debug: starting packer-driver...
2025-7-15 00:47:24-debug: initialize scripting environment...
2025-7-15 00:47:24-debug: [[Executor]] prepare before lock
2025-7-15 00:47:24-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-7-15 00:47:24-debug: [[Executor]] prepare after unlock
2025-7-15 00:47:24-debug: Run asset db hook engine-extends:afterPreStart success!
2025-7-15 00:47:24-debug: Run asset db hook engine-extends:afterPreStart ...
2025-7-15 00:47:24-debug: Start up the 'internal' database...
2025-7-15 00:47:24-debug: Run asset db hook programming:afterPreStart success!
2025-7-15 00:47:24-debug: asset-db-hook-programming-afterPreStart (239ms)
2025-7-15 00:47:24-debug: asset-db:worker-effect-data-processing (140ms)
2025-7-15 00:47:24-debug: asset-db-hook-engine-extends-afterPreStart (140ms)
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:229.57MB, end 242.98MB, increase: 13.40MB
2025-7-15 00:47:24-debug: Start up the 'assets' database...
2025-7-15 00:47:24-debug: asset-db:worker-startup-database[internal] (325ms)
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:232.55MB, end 244.32MB, increase: 11.77MB
2025-7-15 00:47:24-debug: [Assets Memory track]: asset-db:worker-init: startup start:228.18MB, end 244.34MB, increase: 16.15MB
2025-7-15 00:47:24-debug: lazy register asset handler text
2025-7-15 00:47:24-debug: lazy register asset handler json
2025-7-15 00:47:24-debug: lazy register asset handler spine-data
2025-7-15 00:47:24-debug: lazy register asset handler dragonbones-atlas
2025-7-15 00:47:24-debug: lazy register asset handler javascript
2025-7-15 00:47:24-debug: lazy register asset handler dragonbones
2025-7-15 00:47:24-debug: lazy register asset handler typescript
2025-7-15 00:47:24-debug: lazy register asset handler scene
2025-7-15 00:47:24-debug: lazy register asset handler prefab
2025-7-15 00:47:24-debug: lazy register asset handler tiled-map
2025-7-15 00:47:24-debug: lazy register asset handler sprite-frame
2025-7-15 00:47:24-debug: lazy register asset handler buffer
2025-7-15 00:47:24-debug: lazy register asset handler sign-image
2025-7-15 00:47:24-debug: lazy register asset handler image
2025-7-15 00:47:24-debug: lazy register asset handler texture
2025-7-15 00:47:24-debug: lazy register asset handler texture-cube
2025-7-15 00:47:24-debug: lazy register asset handler alpha-image
2025-7-15 00:47:24-debug: lazy register asset handler erp-texture-cube
2025-7-15 00:47:24-debug: lazy register asset handler render-texture
2025-7-15 00:47:24-debug: lazy register asset handler texture-cube-face
2025-7-15 00:47:24-debug: lazy register asset handler rt-sprite-frame
2025-7-15 00:47:24-debug: lazy register asset handler gltf
2025-7-15 00:47:24-debug: lazy register asset handler gltf-mesh
2025-7-15 00:47:24-debug: lazy register asset handler gltf-animation
2025-7-15 00:47:24-debug: lazy register asset handler gltf-skeleton
2025-7-15 00:47:24-debug: lazy register asset handler gltf-material
2025-7-15 00:47:24-debug: lazy register asset handler gltf-embeded-image
2025-7-15 00:47:24-debug: lazy register asset handler gltf-scene
2025-7-15 00:47:24-debug: lazy register asset handler fbx
2025-7-15 00:47:24-debug: lazy register asset handler material
2025-7-15 00:47:24-debug: lazy register asset handler physics-material
2025-7-15 00:47:24-debug: lazy register asset handler effect
2025-7-15 00:47:24-debug: lazy register asset handler effect-header
2025-7-15 00:47:24-debug: lazy register asset handler audio-clip
2025-7-15 00:47:24-debug: lazy register asset handler animation-clip
2025-7-15 00:47:24-debug: lazy register asset handler animation-graph
2025-7-15 00:47:24-debug: lazy register asset handler animation-graph-variant
2025-7-15 00:47:24-debug: lazy register asset handler animation-mask
2025-7-15 00:47:24-debug: lazy register asset handler ttf-font
2025-7-15 00:47:24-debug: lazy register asset handler bitmap-font
2025-7-15 00:47:24-debug: lazy register asset handler particle
2025-7-15 00:47:24-debug: lazy register asset handler sprite-atlas
2025-7-15 00:47:24-debug: lazy register asset handler auto-atlas
2025-7-15 00:47:24-debug: lazy register asset handler label-atlas
2025-7-15 00:47:24-debug: lazy register asset handler render-stage
2025-7-15 00:47:24-debug: lazy register asset handler render-pipeline
2025-7-15 00:47:24-debug: lazy register asset handler render-flow
2025-7-15 00:47:24-debug: lazy register asset handler instantiation-material
2025-7-15 00:47:24-debug: lazy register asset handler instantiation-mesh
2025-7-15 00:47:24-debug: lazy register asset handler instantiation-skeleton
2025-7-15 00:47:24-debug: lazy register asset handler instantiation-animation
2025-7-15 00:47:24-debug: lazy register asset handler video-clip
2025-7-15 00:47:24-debug: lazy register asset handler directory
2025-7-15 00:47:24-debug: lazy register asset handler terrain
2025-7-15 00:47:24-debug: lazy register asset handler *
2025-7-15 00:47:24-debug: asset-db:worker-startup-database[assets] (298ms)
2025-7-15 00:47:24-debug: asset-db:start-database (351ms)
2025-7-15 00:47:24-debug: asset-db:ready (2639ms)
2025-7-15 00:47:24-debug: fix the bug of updateDefaultUserData
2025-7-15 00:47:25-debug: init worker message success
2025-7-15 00:47:25-debug: programming:execute-script (3ms)
2025-7-15 00:47:25-debug: [Build Memory track]: builder:worker-init start:241.57MB, end 254.16MB, increase: 12.59MB
2025-7-15 00:47:25-debug: builder:worker-init (196ms)
2025-7-15 00:47:37-debug: refresh db internal success
2025-7-15 00:47:37-debug: refresh db assets success
2025-7-15 00:47:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:47:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:47:37-debug: asset-db:refresh-all-database (34ms)
2025-7-15 00:47:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 00:47:42-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/traffic.png...
2025-7-15 00:47:42-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/traffic.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:47:42-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/traffic.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:47:42-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/traffic.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:47:42-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img success
2025-7-15 00:47:42-debug: refresh db internal success
2025-7-15 00:47:42-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:47:42-debug: refresh db assets success
2025-7-15 00:47:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:47:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:47:42-debug: asset-db:refresh-all-database (15ms)
2025-7-15 00:48:33-debug: refresh db internal success
2025-7-15 00:48:33-debug: refresh db assets success
2025-7-15 00:48:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:48:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:48:33-debug: asset-db:refresh-all-database (33ms)
2025-7-15 00:48:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 00:48:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 00:48:42-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:42-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-15 00:48:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:44-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:48:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:44-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-15 00:48:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:44-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:48:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:45-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-15 00:48:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:45-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-15 00:48:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:45-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-15 00:48:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:45-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-15 00:48:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:45-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-15 00:48:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:46-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:48:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:46-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:48:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:46-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:48:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:46-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-15 00:48:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:48:46-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:48:52-debug: refresh db internal success
2025-7-15 00:48:52-debug: refresh db assets success
2025-7-15 00:48:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:48:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:48:52-debug: asset-db:refresh-all-database (32ms)
2025-7-15 00:48:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 00:48:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 00:51:00-debug: refresh db internal success
2025-7-15 00:51:00-debug: refresh db assets success
2025-7-15 00:51:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:51:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:51:00-debug: asset-db:refresh-all-database (37ms)
2025-7-15 00:51:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 00:51:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 00:51:22-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png...
2025-7-15 00:51:22-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:22-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:22-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:22-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-15 00:51:22-debug: refresh db internal success
2025-7-15 00:51:22-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:22-debug: refresh db assets success
2025-7-15 00:51:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:51:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:51:22-debug: asset-db:refresh-all-database (15ms)
2025-7-15 00:51:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 00:51:31-debug: start move asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/traffic.png -> /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png...
2025-7-15 00:51:31-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png...
2025-7-15 00:51:31-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:31-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:31-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:31-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-15 00:51:31-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img...
2025-7-15 00:51:31-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:31-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:51:31-debug: move asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/traffic.png -> /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png success
2025-7-15 00:51:31-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets success
2025-7-15 00:52:00-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:52:01-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:52:01-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/traffic.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:52:01-debug: asset-db:reimport-asset83baeef7-746b-4f91-9a84-fd8a58d2b8b3 (73ms)
2025-7-15 00:52:15-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:52:15-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-15 00:52:48-debug: start remove asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png...
2025-7-15 00:52:48-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png...
2025-7-15 00:52:48-debug: %cDestroy%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-7-15 00:52:48-debug: %cDestroy%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-7-15 00:52:48-debug: %cDestroy%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png
background: #ffb8b8; color: #000;
color: #000;
2025-7-15 00:52:48-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-15 00:52:48-debug: remove asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png success
2025-7-15 00:52:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:53:18-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png...
2025-7-15 00:53:18-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:53:18-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:53:18-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:53:18-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-15 00:53:18-debug: refresh db internal success
2025-7-15 00:53:18-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:53:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:53:18-debug: refresh db assets success
2025-7-15 00:53:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:53:18-debug: asset-db:refresh-all-database (17ms)
2025-7-15 00:54:23-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:54:23-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (7ms)
2025-7-15 00:54:24-debug: refresh db internal success
2025-7-15 00:54:24-debug: refresh db assets success
2025-7-15 00:54:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:54:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:54:24-debug: asset-db:refresh-all-database (33ms)
2025-7-15 00:54:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 00:54:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 00:54:35-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:54:35-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-15 00:54:37-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:54:37-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:55:00-debug: refresh db internal success
2025-7-15 00:55:00-debug: refresh db assets success
2025-7-15 00:55:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:55:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:55:00-debug: asset-db:refresh-all-database (34ms)
2025-7-15 00:55:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 00:55:09-debug: Query all assets info in project
2025-7-15 00:55:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 00:55:09-debug: Skip compress image, progress: 0%
2025-7-15 00:55:09-debug: Init all bundles start..., progress: 0%
2025-7-15 00:55:09-debug: Num of bundles: 3..., progress: 0%
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: Init bundle root assets start..., progress: 0%
2025-7-15 00:55:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 00:55:09-debug:   Number of other assets: 399
2025-7-15 00:55:09-debug:   Number of all scripts: 24
2025-7-15 00:55:09-debug:   Number of all scenes: 3
2025-7-15 00:55:09-debug: Init bundle root assets success..., progress: 0%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-15 00:55:09-debug: [Build Memory track]: 查询 Asset Bundle start:198.59MB, end 198.45MB, increase: -143.68KB
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-debug: [Build Memory track]: 查询 Asset Bundle start:198.48MB, end 198.56MB, increase: 89.10KB
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 00:55:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:198.60MB, end 198.62MB, increase: 29.47KB
2025-7-15 00:55:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-15 00:55:09-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-15 00:55:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.15MB, end 198.69MB, increase: -474.56KB
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-15 00:55:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:198.72MB, end 198.86MB, increase: 143.68KB
2025-7-15 00:55:09-debug: Query all assets info in project
2025-7-15 00:55:09-debug: Query all assets info in project
2025-7-15 00:55:09-debug: Query all assets info in project
2025-7-15 00:55:09-debug: Query all assets info in project
2025-7-15 00:55:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 00:55:09-debug: Skip compress image, progress: 0%
2025-7-15 00:55:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 00:55:09-debug: Skip compress image, progress: 0%
2025-7-15 00:55:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 00:55:09-debug: Skip compress image, progress: 0%
2025-7-15 00:55:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 00:55:09-debug: Skip compress image, progress: 0%
2025-7-15 00:55:09-debug: Init all bundles start..., progress: 0%
2025-7-15 00:55:09-debug: Num of bundles: 3..., progress: 0%
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: Init bundle root assets start..., progress: 0%
2025-7-15 00:55:09-debug: Init all bundles start..., progress: 0%
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 00:55:09-debug: Num of bundles: 3..., progress: 0%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: Init bundle root assets start..., progress: 0%
2025-7-15 00:55:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 00:55:09-debug:   Number of all scripts: 24
2025-7-15 00:55:09-debug:   Number of other assets: 399
2025-7-15 00:55:09-debug: Init bundle root assets success..., progress: 0%
2025-7-15 00:55:09-debug:   Number of all scenes: 3
2025-7-15 00:55:09-debug: Init all bundles start..., progress: 0%
2025-7-15 00:55:09-debug: Num of bundles: 3..., progress: 0%
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug:   Number of all scripts: 24
2025-7-15 00:55:09-debug: Init bundle root assets success..., progress: 0%
2025-7-15 00:55:09-debug:   Number of other assets: 399
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-15 00:55:09-debug: Init bundle root assets start..., progress: 0%
2025-7-15 00:55:09-debug: [Build Memory track]: 查询 Asset Bundle start:198.86MB, end 199.95MB, increase: 1.09MB
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-15 00:55:09-debug:   Number of all scenes: 3
2025-7-15 00:55:09-debug: Num of bundles: 3..., progress: 0%
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 00:55:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 00:55:09-debug: Init all bundles start..., progress: 0%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: [Build Memory track]: 查询 Asset Bundle start:198.83MB, end 198.87MB, increase: 40.45KB
2025-7-15 00:55:09-debug: Init bundle root assets start..., progress: 0%
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-15 00:55:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 00:55:09-debug: Init bundle root assets success..., progress: 0%
2025-7-15 00:55:09-debug:   Number of all scenes: 3
2025-7-15 00:55:09-debug:   Number of other assets: 399
2025-7-15 00:55:09-debug:   Number of all scripts: 24
2025-7-15 00:55:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 00:55:09-debug: Init bundle root assets success..., progress: 0%
2025-7-15 00:55:09-debug:   Number of all scripts: 24
2025-7-15 00:55:09-debug:   Number of all scenes: 3
2025-7-15 00:55:09-debug:   Number of other assets: 399
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 00:55:09-debug: [Build Memory track]: 查询 Asset Bundle start:198.91MB, end 199.48MB, increase: 590.91KB
2025-7-15 00:55:09-debug: [Build Memory track]: 查询 Asset Bundle start:199.88MB, end 199.20MB, increase: -702.01KB
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-7-15 00:55:09-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 00:55:09-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 00:55:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 00:55:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:198.88MB, end 199.01MB, increase: 127.25KB
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 00:55:09-debug: [Build Memory track]: 查询 Asset Bundle start:198.92MB, end 199.06MB, increase: 141.20KB
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-15 00:55:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 00:55:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.04MB, end 199.13MB, increase: 92.14KB
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.16MB, end 199.19MB, increase: 24.54KB
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.55MB, end 199.37MB, increase: -189.27KB
2025-7-15 00:55:09-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.40MB, end 199.42MB, increase: 27.30KB
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 00:55:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-15 00:55:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.46MB, end 199.53MB, increase: 80.06KB
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-15 00:55:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.57MB, end 199.59MB, increase: 24.70KB
2025-7-15 00:55:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 00:55:09-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-15 00:55:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 00:55:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.62MB, end 199.21MB, increase: -420.88KB
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 00:55:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-15 00:55:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.24MB, end 199.12MB, increase: -121.56KB
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-15 00:55:09-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-15 00:55:23-debug: refresh db internal success
2025-7-15 00:55:23-debug: refresh db assets success
2025-7-15 00:55:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:55:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:55:23-debug: asset-db:refresh-all-database (31ms)
2025-7-15 00:57:31-debug: refresh db internal success
2025-7-15 00:57:31-debug: refresh db assets success
2025-7-15 00:57:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 00:57:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 00:57:31-debug: asset-db:refresh-all-database (31ms)
2025-7-15 00:57:34-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red-001.png...
2025-7-15 00:57:34-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red-001.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:34-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red-001.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:34-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red-001.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:34-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-15 00:57:34-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:38-debug: start move asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red-001.png -> /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png...
2025-7-15 00:57:38-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png...
2025-7-15 00:57:38-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:38-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:38-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:38-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-15 00:57:38-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu...
2025-7-15 00:57:38-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:57:38-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img success
2025-7-15 00:57:38-debug: move asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red-001.png -> /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png success
2025-7-15 00:58:13-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:13-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:13-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:13-debug: asset-db:reimport-asset593904f2-fa85-498f-9b0e-9f79890c24fd (15ms)
2025-7-15 00:58:16-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:16-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:16-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:16-debug: asset-db:reimport-asset593904f2-fa85-498f-9b0e-9f79890c24fd (18ms)
2025-7-15 00:58:31-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:31-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:31-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:31-debug: asset-db:reimport-asset593904f2-fa85-498f-9b0e-9f79890c24fd (18ms)
2025-7-15 00:58:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:58:44-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 00:59:19-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:59:19-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-15 00:59:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 00:59:27-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-15 01:00:22-debug: refresh db internal success
2025-7-15 01:00:22-debug: refresh db assets success
2025-7-15 01:00:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 01:00:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 01:00:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 01:00:22-debug: asset-db:refresh-all-database (30ms)
2025-7-15 01:00:25-debug: Query all assets info in project
2025-7-15 01:00:25-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 01:00:25-debug: Skip compress image, progress: 0%
2025-7-15 01:00:25-debug: Init all bundles start..., progress: 0%
2025-7-15 01:00:25-debug: Num of bundles: 3..., progress: 0%
2025-7-15 01:00:25-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 01:00:25-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 01:00:25-debug: Init bundle root assets start..., progress: 0%
2025-7-15 01:00:25-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 01:00:25-debug:   Number of all scripts: 24
2025-7-15 01:00:25-debug:   Number of other assets: 402
2025-7-15 01:00:25-debug: Init bundle root assets success..., progress: 0%
2025-7-15 01:00:25-debug:   Number of all scenes: 3
2025-7-15 01:00:25-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-15 01:00:25-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-15 01:00:25-debug: [Build Memory track]: 查询 Asset Bundle start:201.76MB, end 201.67MB, increase: -86.04KB
2025-7-15 01:00:25-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 01:00:25-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 01:00:25-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 01:00:25-debug: [Build Memory track]: 查询 Asset Bundle start:201.70MB, end 201.07MB, increase: -644.63KB
2025-7-15 01:00:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 01:00:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 01:00:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 01:00:25-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-15 01:00:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:201.09MB, end 201.12MB, increase: 23.82KB
2025-7-15 01:00:25-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 01:00:25-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 01:00:25-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-15 01:00:25-debug: [Build Memory track]: 填充脚本数据到 settings.json start:201.14MB, end 201.15MB, increase: 14.77KB
2025-7-15 01:00:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 01:00:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 01:00:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 01:00:25-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-15 01:00:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:201.18MB, end 201.31MB, increase: 137.25KB
2025-7-15 01:00:34-debug: refresh db internal success
2025-7-15 01:00:34-debug: refresh db assets success
2025-7-15 01:00:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 01:00:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 01:00:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 01:00:34-debug: asset-db:refresh-all-database (34ms)
2025-7-15 01:00:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 01:01:23-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:01:23-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-15 01:01:47-debug: refresh db internal success
2025-7-15 01:01:47-debug: refresh db assets success
2025-7-15 01:01:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 01:01:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 01:01:47-debug: asset-db:refresh-all-database (23ms)
2025-7-15 01:02:10-debug: refresh db internal success
2025-7-15 01:02:10-debug: refresh db assets success
2025-7-15 01:02:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 01:02:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 01:02:10-debug: asset-db:refresh-all-database (29ms)
2025-7-15 01:02:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 01:02:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 01:02:14-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/green.png...
2025-7-15 01:02:14-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/green.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:02:14-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/green.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:02:14-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/green.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:02:14-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-15 01:02:14-debug: refresh db internal success
2025-7-15 01:02:14-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:02:14-debug: refresh db assets success
2025-7-15 01:02:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 01:02:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 01:02:14-debug: asset-db:refresh-all-database (19ms)
2025-7-15 01:02:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 01:02:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 01:03:03-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:03:03-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-15 01:03:18-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:03:18-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-15 01:03:37-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:03:37-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-15 01:03:40-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:03:40-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (7ms)
2025-7-15 01:03:40-debug: Query all assets info in project
2025-7-15 01:03:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 01:03:40-debug: Skip compress image, progress: 0%
2025-7-15 01:03:40-debug: Init all bundles start..., progress: 0%
2025-7-15 01:03:40-debug: Init bundle root assets start..., progress: 0%
2025-7-15 01:03:40-debug: Num of bundles: 3..., progress: 0%
2025-7-15 01:03:40-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 01:03:40-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 01:03:40-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 01:03:40-debug:   Number of other assets: 405
2025-7-15 01:03:40-debug:   Number of all scripts: 24
2025-7-15 01:03:40-debug: Init bundle root assets success..., progress: 0%
2025-7-15 01:03:40-debug:   Number of all scenes: 3
2025-7-15 01:03:40-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-15 01:03:40-debug: [Build Memory track]: 查询 Asset Bundle start:203.96MB, end 203.83MB, increase: -125.21KB
2025-7-15 01:03:40-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 01:03:40-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-15 01:03:40-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 01:03:40-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 01:03:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 01:03:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 01:03:40-debug: [Build Memory track]: 查询 Asset Bundle start:203.86MB, end 203.38MB, increase: -493.63KB
2025-7-15 01:03:40-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-15 01:03:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.26MB, end 203.27MB, increase: 15.82KB
2025-7-15 01:03:40-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 01:03:40-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 01:03:40-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-15 01:03:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 01:03:40-debug: [Build Memory track]: 填充脚本数据到 settings.json start:203.30MB, end 203.32MB, increase: 22.24KB
2025-7-15 01:03:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 01:03:40-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-15 01:03:40-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-15 01:03:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.35MB, end 203.47MB, increase: 126.10KB
2025-7-15 01:03:47-debug: refresh db internal success
2025-7-15 01:03:47-debug: refresh db assets success
2025-7-15 01:03:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 01:03:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 01:03:47-debug: asset-db:refresh-all-database (20ms)
2025-7-15 01:03:53-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:03:53-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-15 01:04:02-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-15 01:04:02-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-15 01:04:21-debug: refresh db internal success
2025-7-15 01:04:21-debug: refresh db assets success
2025-7-15 01:04:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 01:04:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 01:04:21-debug: asset-db:refresh-all-database (31ms)
2025-7-15 01:04:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 01:04:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 10:05:41-debug: refresh db internal success
2025-7-15 10:05:41-debug: refresh db assets success
2025-7-15 10:05:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 10:05:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 10:05:41-debug: asset-db:refresh-all-database (32ms)
2025-7-15 10:05:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 10:05:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 10:06:15-debug: Query all assets info in project
2025-7-15 10:06:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 10:06:15-debug: Skip compress image, progress: 0%
2025-7-15 10:06:15-debug: Num of bundles: 3..., progress: 0%
2025-7-15 10:06:15-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 10:06:15-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 10:06:15-debug: Init bundle root assets start..., progress: 0%
2025-7-15 10:06:15-debug: Init all bundles start..., progress: 0%
2025-7-15 10:06:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 10:06:15-debug: Init bundle root assets success..., progress: 0%
2025-7-15 10:06:15-debug:   Number of all scripts: 24
2025-7-15 10:06:15-debug:   Number of other assets: 405
2025-7-15 10:06:15-debug:   Number of all scenes: 3
2025-7-15 10:06:15-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-15 10:06:15-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-15 10:06:15-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 10:06:15-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 10:06:15-debug: [Build Memory track]: 查询 Asset Bundle start:205.42MB, end 205.15MB, increase: -271.02KB
2025-7-15 10:06:15-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 10:06:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 10:06:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 10:06:15-debug: [Build Memory track]: 查询 Asset Bundle start:205.18MB, end 205.26MB, increase: 84.09KB
2025-7-15 10:06:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-15 10:06:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.28MB, end 205.30MB, increase: 15.68KB
2025-7-15 10:06:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 10:06:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 10:06:15-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-15 10:06:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 10:06:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 10:06:15-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.33MB, end 205.34MB, increase: 18.75KB
2025-7-15 10:06:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 10:06:15-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-15 10:06:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.37MB, end 205.49MB, increase: 122.67KB
2025-7-15 10:06:48-debug: Query all assets info in project
2025-7-15 10:06:48-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 10:06:48-debug: Skip compress image, progress: 0%
2025-7-15 10:06:48-debug: Init all bundles start..., progress: 0%
2025-7-15 10:06:48-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 10:06:48-debug: Num of bundles: 3..., progress: 0%
2025-7-15 10:06:48-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 10:06:48-debug: Init bundle root assets start..., progress: 0%
2025-7-15 10:06:48-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 10:06:48-debug:   Number of other assets: 405
2025-7-15 10:06:48-debug: Init bundle root assets success..., progress: 0%
2025-7-15 10:06:48-debug:   Number of all scenes: 3
2025-7-15 10:06:48-debug:   Number of all scripts: 24
2025-7-15 10:06:48-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-15 10:06:48-debug: [Build Memory track]: 查询 Asset Bundle start:205.11MB, end 205.77MB, increase: 676.84KB
2025-7-15 10:06:48-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-15 10:06:48-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 10:06:48-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 10:06:48-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-15 10:06:48-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-7-15 10:06:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 10:06:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 10:06:48-debug: [Build Memory track]: 查询 Asset Bundle start:205.80MB, end 205.14MB, increase: -669.45KB
2025-7-15 10:06:48-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-15 10:06:48-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 10:06:48-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 10:06:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.17MB, end 205.19MB, increase: 17.18KB
2025-7-15 10:06:48-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-15 10:06:48-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.21MB, end 205.23MB, increase: 23.07KB
2025-7-15 10:06:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 10:06:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 10:06:48-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-15 10:06:48-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-15 10:06:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.26MB, end 205.38MB, increase: 120.39KB
2025-7-15 10:07:03-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/86-f.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:03-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/86-f.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:03-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/86-f.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:03-debug: asset-db:reimport-asset7e95e032-b6ae-4de9-a8ab-c81b24b5fb5f (31ms)
2025-7-15 10:07:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/86-t.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:12-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/86-t.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:12-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/86-t.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:12-debug: asset-db:reimport-asseted611db6-e51e-4a53-b624-848dd45ace21 (26ms)
2025-7-15 10:07:15-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/car1-crash.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:16-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/car1-crash.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:16-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/car1-crash.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:16-debug: asset-db:reimport-asseta238f344-7c25-4d83-a0ec-8c85e46d3ec8 (71ms)
2025-7-15 10:07:19-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/car.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:19-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/car.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:19-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/car.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:19-debug: asset-db:reimport-asset49e36654-b2c3-47d9-afeb-5db28286a1b0 (62ms)
2025-7-15 10:07:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/mini-f.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:25-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/mini-f.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:25-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/mini-f.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:25-debug: asset-db:reimport-asset35990206-f1f7-49c0-91d6-a50921fc8128 (55ms)
2025-7-15 10:07:28-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/rx7-f.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:28-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/rx7-f.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:28-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/rx7-f.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:28-debug: asset-db:reimport-asset0b33dc38-0c13-4f55-b754-27d76610db59 (25ms)
2025-7-15 10:07:34-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/rx7-t.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:34-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/rx7-t.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:34-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/rx7-t.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:07:34-debug: asset-db:reimport-asset38414c6f-ce08-40c3-b0a0-4f24ce1031de (23ms)
2025-7-15 10:28:54-debug: refresh db internal success
2025-7-15 10:28:54-debug: refresh db assets success
2025-7-15 10:28:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 10:28:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 10:28:54-debug: asset-db:refresh-all-database (28ms)
2025-7-15 10:28:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 10:28:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 10:29:01-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/taycan-f.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:01-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/taycan-f.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:01-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/taycan-f.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:01-debug: asset-db:reimport-asseta9e97b11-149f-46e6-a990-c95fd6d91cd7 (30ms)
2025-7-15 10:29:04-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/taycan-t.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:04-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/taycan-t.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:04-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/taycan-t.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:04-debug: asset-db:reimport-assetd6c5db05-a7b5-4c39-b29f-89c86c315dcf (21ms)
2025-7-15 10:29:07-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/z370.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:07-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/z370.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:07-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/z370.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:07-debug: asset-db:reimport-asset5308cc80-fcde-4667-baa6-dbcdc87fd132 (26ms)
2025-7-15 10:29:11-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/z370-t.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:11-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/z370-t.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:11-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/z370-t.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:11-debug: asset-db:reimport-assetb9e95f02-4aec-4c01-8343-1c6ce036b0c6 (21ms)
2025-7-15 10:29:17-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:17-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:17-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/red.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:18-debug: asset-db:reimport-asset4cf1948a-ffca-426e-9c43-1e7c864a1b60 (17ms)
2025-7-15 10:29:21-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/green.png
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:21-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/green.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:21-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/green.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-15 10:29:21-debug: asset-db:reimport-asset1c18f6e7-3b13-401b-85a1-746bf7284949 (23ms)
2025-7-15 10:29:37-debug: Query all assets info in project
2025-7-15 10:29:37-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 10:29:37-debug: Skip compress image, progress: 0%
2025-7-15 10:29:37-debug: Init all bundles start..., progress: 0%
2025-7-15 10:29:37-debug: Num of bundles: 3..., progress: 0%
2025-7-15 10:29:37-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 10:29:37-debug: Init bundle root assets start..., progress: 0%
2025-7-15 10:29:37-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 10:29:37-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 10:29:37-debug:   Number of other assets: 405
2025-7-15 10:29:37-debug:   Number of all scripts: 24
2025-7-15 10:29:37-debug: Init bundle root assets success..., progress: 0%
2025-7-15 10:29:37-debug:   Number of all scenes: 3
2025-7-15 10:29:37-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-15 10:29:37-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 10:29:37-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 10:29:37-debug: [Build Memory track]: 查询 Asset Bundle start:198.25MB, end 198.22MB, increase: -28.64KB
2025-7-15 10:29:37-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-15 10:29:37-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 10:29:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 10:29:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 10:29:37-debug: [Build Memory track]: 查询 Asset Bundle start:198.25MB, end 198.33MB, increase: 86.75KB
2025-7-15 10:29:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 10:29:37-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-15 10:29:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:198.36MB, end 198.38MB, increase: 23.56KB
2025-7-15 10:29:37-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 10:29:37-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 10:29:37-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-15 10:29:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 10:29:37-debug: [Build Memory track]: 填充脚本数据到 settings.json start:198.41MB, end 198.43MB, increase: 18.31KB
2025-7-15 10:29:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 10:29:37-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-15 10:29:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:198.45MB, end 198.58MB, increase: 132.41KB
2025-7-15 10:29:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 18:40:56-debug: refresh db internal success
2025-7-15 18:40:56-debug: refresh db assets success
2025-7-15 18:40:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-15 18:40:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-15 18:40:56-debug: asset-db:refresh-all-database (51ms)
2025-7-15 18:40:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-15 18:40:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-15 18:41:56-debug: Query all assets info in project
2025-7-15 18:41:56-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-15 18:41:56-debug: Skip compress image, progress: 0%
2025-7-15 18:41:56-debug: Init all bundles start..., progress: 0%
2025-7-15 18:41:56-debug: 查询 Asset Bundle start, progress: 0%
2025-7-15 18:41:56-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 18:41:56-debug: Num of bundles: 3..., progress: 0%
2025-7-15 18:41:56-debug: Init bundle root assets start..., progress: 0%
2025-7-15 18:41:56-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-15 18:41:56-debug:   Number of all scenes: 3
2025-7-15 18:41:56-debug:   Number of other assets: 405
2025-7-15 18:41:56-debug:   Number of all scripts: 24
2025-7-15 18:41:56-debug: Init bundle root assets success..., progress: 0%
2025-7-15 18:41:56-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-15 18:41:56-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-15 18:41:56-debug: [Build Memory track]: 查询 Asset Bundle start:199.36MB, end 199.09MB, increase: -272.26KB
2025-7-15 18:41:56-debug: 查询 Asset Bundle start, progress: 5%
2025-7-15 18:41:56-debug: // ---- build task 查询 Asset Bundle ----
2025-7-15 18:41:56-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-15 18:41:56-debug: [Build Memory track]: 查询 Asset Bundle start:199.11MB, end 199.20MB, increase: 85.52KB
2025-7-15 18:41:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 18:41:56-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-15 18:41:56-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-15 18:41:56-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.22MB, end 199.24MB, increase: 16.11KB
2025-7-15 18:41:56-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-15 18:41:56-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-15 18:41:56-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-15 18:41:56-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-15 18:41:56-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.26MB, end 199.29MB, increase: 23.58KB
2025-7-15 18:41:56-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-15 18:41:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-15 18:41:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-15 18:41:56-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.31MB, end 199.44MB, increase: 128.44KB
2025-7-15 18:41:56-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
