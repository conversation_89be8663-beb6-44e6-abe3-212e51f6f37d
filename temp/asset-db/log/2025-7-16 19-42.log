2025-7-16 19:42:17-debug: start **** info
2025-7-16 19:42:18-log: Cannot access game frame or container.
2025-7-16 19:42:18-debug: asset-db:require-engine-code (381ms)
2025-7-16 19:42:18-log: meshopt wasm decoder initialized
2025-7-16 19:42:18-log: [bullet]:bullet wasm lib loaded.
2025-7-16 19:42:18-log: [box2d]:box2d wasm lib loaded.
2025-7-16 19:42:18-log: Cocos Creator v3.8.6
2025-7-16 19:42:18-log: Using legacy pipeline
2025-7-16 19:42:18-log: Forward render pipeline initialized.
2025-7-16 19:42:18-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:29.34MB, end 80.24MB, increase: 50.91MB
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.26MB, end 289.77MB, increase: 209.51MB
2025-7-16 19:42:18-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.08MB, end 88.69MB, increase: 7.61MB
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db-plugin-register: builder start:88.71MB, end 288.76MB, increase: 200.05MB
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.96MB, end 289.55MB, increase: 208.59MB
2025-7-16 19:42:19-debug: run package(huawei-agc) handler(enable) start
2025-7-16 19:42:19-debug: run package(huawei-agc) handler(enable) success!
2025-7-16 19:42:19-debug: run package(huawei-quick-game) handler(enable) success!
2025-7-16 19:42:19-debug: run package(ios) handler(enable) start
2025-7-16 19:42:19-debug: run package(linux) handler(enable) success!
2025-7-16 19:42:19-debug: run package(mac) handler(enable) start
2025-7-16 19:42:19-debug: run package(mac) handler(enable) success!
2025-7-16 19:42:19-debug: run package(migu-mini-game) handler(enable) start
2025-7-16 19:42:19-debug: run package(migu-mini-game) handler(enable) success!
2025-7-16 19:42:19-debug: run package(native) handler(enable) start
2025-7-16 19:42:19-debug: run package(native) handler(enable) success!
2025-7-16 19:42:19-debug: run package(ohos) handler(enable) start
2025-7-16 19:42:19-debug: run package(ohos) handler(enable) success!
2025-7-16 19:42:19-debug: run package(oppo-mini-game) handler(enable) start
2025-7-16 19:42:19-debug: run package(oppo-mini-game) handler(enable) success!
2025-7-16 19:42:19-debug: run package(runtime-dev-tools) handler(enable) start
2025-7-16 19:42:19-debug: run package(runtime-dev-tools) handler(enable) success!
2025-7-16 19:42:19-debug: run package(taobao-mini-game) handler(enable) success!
2025-7-16 19:42:19-debug: run package(taobao-mini-game) handler(enable) start
2025-7-16 19:42:19-debug: run package(vivo-mini-game) handler(enable) start
2025-7-16 19:42:19-debug: run package(vivo-mini-game) handler(enable) success!
2025-7-16 19:42:19-debug: run package(web-desktop) handler(enable) start
2025-7-16 19:42:19-debug: run package(web-desktop) handler(enable) success!
2025-7-16 19:42:19-debug: run package(web-mobile) handler(enable) start
2025-7-16 19:42:19-debug: run package(web-mobile) handler(enable) success!
2025-7-16 19:42:19-debug: run package(huawei-quick-game) handler(enable) start
2025-7-16 19:42:19-debug: run package(wechatgame) handler(enable) success!
2025-7-16 19:42:19-debug: run package(wechatprogram) handler(enable) success!
2025-7-16 19:42:19-debug: run package(wechatprogram) handler(enable) start
2025-7-16 19:42:19-debug: run package(windows) handler(enable) start
2025-7-16 19:42:19-debug: run package(windows) handler(enable) success!
2025-7-16 19:42:19-debug: run package(linux) handler(enable) start
2025-7-16 19:42:19-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-7-16 19:42:19-debug: run package(cocos-service) handler(enable) success!
2025-7-16 19:42:19-debug: run package(cocos-service) handler(enable) start
2025-7-16 19:42:19-debug: run package(im-plugin) handler(enable) start
2025-7-16 19:42:19-debug: run package(im-plugin) handler(enable) success!
2025-7-16 19:42:19-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-7-16 19:42:19-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-7-16 19:42:19-debug: run package(ios) handler(enable) success!
2025-7-16 19:42:19-debug: run package(wechatgame) handler(enable) start
2025-7-16 19:42:19-debug: run package(placeholder) handler(enable) success!
2025-7-16 19:42:19-debug: asset-db:worker-init: initPlugin (889ms)
2025-7-16 19:42:19-debug: run package(xiaomi-quick-game) handler(enable) start
2025-7-16 19:42:19-debug: run package(placeholder) handler(enable) start
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db:worker-init start:29.33MB, end 289.27MB, increase: 259.94MB
2025-7-16 19:42:19-debug: Run asset db hook programming:beforePreStart ...
2025-7-16 19:42:19-debug: Run asset db hook engine-extends:beforePreStart success!
2025-7-16 19:42:19-debug: Run asset db hook programming:beforePreStart success!
2025-7-16 19:42:19-debug: Run asset db hook engine-extends:beforePreStart ...
2025-7-16 19:42:19-debug: asset-db-hook-programming-beforePreStart (50ms)
2025-7-16 19:42:19-debug: asset-db:worker-init (1370ms)
2025-7-16 19:42:19-debug: asset-db-hook-engine-extends-beforePreStart (50ms)
2025-7-16 19:42:19-debug: Preimport db internal success
2025-7-16 19:42:19-debug: Preimport db assets success
2025-7-16 19:42:19-debug: Run asset db hook programming:afterPreStart ...
2025-7-16 19:42:19-debug: starting packer-driver...
2025-7-16 19:42:19-debug: initialize scripting environment...
2025-7-16 19:42:19-debug: [[Executor]] prepare before lock
2025-7-16 19:42:19-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-7-16 19:42:19-debug: [[Executor]] prepare after unlock
2025-7-16 19:42:19-debug: Run asset db hook programming:afterPreStart success!
2025-7-16 19:42:19-debug: Run asset db hook engine-extends:afterPreStart ...
2025-7-16 19:42:19-debug: Run asset db hook engine-extends:afterPreStart success!
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db:worker-init: preStart start:289.28MB, end 293.84MB, increase: 4.56MB
2025-7-16 19:42:19-debug: Start up the 'internal' database...
2025-7-16 19:42:19-debug: asset-db-hook-programming-afterPreStart (285ms)
2025-7-16 19:42:19-debug: asset-db-hook-engine-extends-afterPreStart (174ms)
2025-7-16 19:42:19-debug: asset-db:worker-effect-data-processing (174ms)
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:289.36MB, end 307.68MB, increase: 18.32MB
2025-7-16 19:42:19-debug: Start up the 'assets' database...
2025-7-16 19:42:19-debug: asset-db:worker-startup-database[internal] (404ms)
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:292.40MB, end 309.25MB, increase: 16.85MB
2025-7-16 19:42:19-debug: [Assets Memory track]: asset-db:worker-init: startup start:293.85MB, end 309.27MB, increase: 15.42MB
2025-7-16 19:42:19-debug: lazy register asset handler json
2025-7-16 19:42:19-debug: lazy register asset handler spine-data
2025-7-16 19:42:19-debug: lazy register asset handler dragonbones
2025-7-16 19:42:19-debug: lazy register asset handler text
2025-7-16 19:42:19-debug: lazy register asset handler terrain
2025-7-16 19:42:19-debug: lazy register asset handler javascript
2025-7-16 19:42:19-debug: lazy register asset handler *
2025-7-16 19:42:19-debug: lazy register asset handler typescript
2025-7-16 19:42:19-debug: lazy register asset handler scene
2025-7-16 19:42:19-debug: lazy register asset handler prefab
2025-7-16 19:42:19-debug: lazy register asset handler sprite-frame
2025-7-16 19:42:19-debug: lazy register asset handler tiled-map
2025-7-16 19:42:19-debug: lazy register asset handler buffer
2025-7-16 19:42:19-debug: lazy register asset handler sign-image
2025-7-16 19:42:19-debug: lazy register asset handler alpha-image
2025-7-16 19:42:19-debug: lazy register asset handler texture
2025-7-16 19:42:19-debug: lazy register asset handler texture-cube
2025-7-16 19:42:19-debug: lazy register asset handler erp-texture-cube
2025-7-16 19:42:19-debug: lazy register asset handler render-texture
2025-7-16 19:42:19-debug: lazy register asset handler texture-cube-face
2025-7-16 19:42:19-debug: lazy register asset handler rt-sprite-frame
2025-7-16 19:42:19-debug: lazy register asset handler gltf
2025-7-16 19:42:19-debug: lazy register asset handler gltf-mesh
2025-7-16 19:42:19-debug: lazy register asset handler gltf-animation
2025-7-16 19:42:19-debug: lazy register asset handler gltf-skeleton
2025-7-16 19:42:19-debug: lazy register asset handler gltf-material
2025-7-16 19:42:19-debug: lazy register asset handler gltf-scene
2025-7-16 19:42:19-debug: lazy register asset handler gltf-embeded-image
2025-7-16 19:42:19-debug: lazy register asset handler material
2025-7-16 19:42:19-debug: lazy register asset handler physics-material
2025-7-16 19:42:19-debug: lazy register asset handler effect-header
2025-7-16 19:42:19-debug: lazy register asset handler effect
2025-7-16 19:42:19-debug: lazy register asset handler audio-clip
2025-7-16 19:42:19-debug: lazy register asset handler animation-graph
2025-7-16 19:42:19-debug: lazy register asset handler animation-clip
2025-7-16 19:42:19-debug: lazy register asset handler animation-graph-variant
2025-7-16 19:42:19-debug: lazy register asset handler ttf-font
2025-7-16 19:42:19-debug: lazy register asset handler bitmap-font
2025-7-16 19:42:19-debug: lazy register asset handler sprite-atlas
2025-7-16 19:42:19-debug: lazy register asset handler particle
2025-7-16 19:42:19-debug: lazy register asset handler label-atlas
2025-7-16 19:42:19-debug: lazy register asset handler auto-atlas
2025-7-16 19:42:19-debug: lazy register asset handler render-pipeline
2025-7-16 19:42:19-debug: lazy register asset handler render-flow
2025-7-16 19:42:19-debug: lazy register asset handler render-stage
2025-7-16 19:42:19-debug: lazy register asset handler instantiation-material
2025-7-16 19:42:19-debug: lazy register asset handler instantiation-mesh
2025-7-16 19:42:19-debug: lazy register asset handler fbx
2025-7-16 19:42:19-debug: lazy register asset handler instantiation-skeleton
2025-7-16 19:42:19-debug: lazy register asset handler animation-mask
2025-7-16 19:42:19-debug: lazy register asset handler instantiation-animation
2025-7-16 19:42:19-debug: lazy register asset handler video-clip
2025-7-16 19:42:19-debug: lazy register asset handler image
2025-7-16 19:42:19-debug: lazy register asset handler dragonbones-atlas
2025-7-16 19:42:19-debug: lazy register asset handler directory
2025-7-16 19:42:19-debug: asset-db:worker-startup-database[assets] (364ms)
2025-7-16 19:42:19-debug: asset-db:start-database (433ms)
2025-7-16 19:42:19-debug: asset-db:ready (3026ms)
2025-7-16 19:42:19-debug: fix the bug of updateDefaultUserData
2025-7-16 19:42:19-debug: init worker message success
2025-7-16 19:42:19-debug: programming:execute-script (3ms)
2025-7-16 19:42:19-debug: [Build Memory track]: builder:worker-init start:308.22MB, end 322.25MB, increase: 14.04MB
2025-7-16 19:42:19-debug: builder:worker-init (214ms)
2025-7-16 19:42:54-debug: refresh db internal success
2025-7-16 19:42:54-debug: refresh db assets success
2025-7-16 19:42:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:42:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:42:54-debug: asset-db:refresh-all-database (37ms)
2025-7-16 19:43:51-debug: refresh db internal success
2025-7-16 19:43:51-debug: refresh db assets success
2025-7-16 19:43:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:43:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:43:51-debug: asset-db:refresh-all-database (35ms)
2025-7-16 19:43:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:43:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:43:55-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:43:55-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-16 19:44:16-debug: refresh db internal success
2025-7-16 19:44:16-debug: refresh db assets success
2025-7-16 19:44:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:44:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:44:16-debug: asset-db:refresh-all-database (35ms)
2025-7-16 19:44:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:44:34-debug: Query all assets info in project
2025-7-16 19:44:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 19:44:34-debug: Skip compress image, progress: 0%
2025-7-16 19:44:34-debug: Init all bundles start..., progress: 0%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 19:44:34-debug: Init bundle root assets start..., progress: 0%
2025-7-16 19:44:34-debug: Num of bundles: 3..., progress: 0%
2025-7-16 19:44:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 19:44:34-debug:   Number of other assets: 405
2025-7-16 19:44:34-debug:   Number of all scripts: 24
2025-7-16 19:44:34-debug: Init bundle root assets success..., progress: 0%
2025-7-16 19:44:34-debug:   Number of all scenes: 3
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 5%
2025-7-16 19:44:34-debug: [Build Memory track]: 查询 Asset Bundle start:199.77MB, end 199.71MB, increase: -61.03KB
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-debug: [Build Memory track]: 查询 Asset Bundle start:199.75MB, end 199.83MB, increase: 89.36KB
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-16 19:44:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.87MB, end 199.91MB, increase: 41.71KB
2025-7-16 19:44:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 19:44:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-16 19:44:34-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-16 19:44:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.95MB, end 199.96MB, increase: 18.46KB
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-16 19:44:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.00MB, end 200.14MB, increase: 144.99KB
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-16 19:44:34-debug: Query all assets info in project
2025-7-16 19:44:34-debug: Query all assets info in project
2025-7-16 19:44:34-debug: Query all assets info in project
2025-7-16 19:44:34-debug: Query all assets info in project
2025-7-16 19:44:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 19:44:34-debug: Skip compress image, progress: 0%
2025-7-16 19:44:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 19:44:34-debug: Skip compress image, progress: 0%
2025-7-16 19:44:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 19:44:34-debug: Skip compress image, progress: 0%
2025-7-16 19:44:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 19:44:34-debug: Skip compress image, progress: 0%
2025-7-16 19:44:34-debug: Init all bundles start..., progress: 0%
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-debug: Num of bundles: 3..., progress: 0%
2025-7-16 19:44:34-debug: Init bundle root assets start..., progress: 0%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-debug: Num of bundles: 3..., progress: 0%
2025-7-16 19:44:34-debug: Init bundle root assets start..., progress: 0%
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 19:44:34-debug: Init all bundles start..., progress: 0%
2025-7-16 19:44:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 19:44:34-debug: Init bundle root assets success..., progress: 0%
2025-7-16 19:44:34-debug: Init all bundles start..., progress: 0%
2025-7-16 19:44:34-debug: Num of bundles: 3..., progress: 0%
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-debug: Init bundle root assets start..., progress: 0%
2025-7-16 19:44:34-debug:   Number of all scripts: 24
2025-7-16 19:44:34-debug:   Number of all scenes: 3
2025-7-16 19:44:34-debug:   Number of other assets: 405
2025-7-16 19:44:34-debug: Init all bundles start..., progress: 0%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-debug: Num of bundles: 3..., progress: 0%
2025-7-16 19:44:34-debug: Init bundle root assets start..., progress: 0%
2025-7-16 19:44:34-debug: [Build Memory track]: 查询 Asset Bundle start:200.16MB, end 200.19MB, increase: 37.34KB
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-16 19:44:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 19:44:34-debug:   Number of all scripts: 24
2025-7-16 19:44:34-debug:   Number of other assets: 405
2025-7-16 19:44:34-debug:   Number of all scenes: 3
2025-7-16 19:44:34-debug: Init bundle root assets success..., progress: 0%
2025-7-16 19:44:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 19:44:34-debug: Init bundle root assets success..., progress: 0%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-16 19:44:34-debug: [Build Memory track]: 查询 Asset Bundle start:200.22MB, end 200.80MB, increase: 589.54KB
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-debug:   Number of other assets: 405
2025-7-16 19:44:34-debug:   Number of all scenes: 3
2025-7-16 19:44:34-debug:   Number of all scripts: 24
2025-7-16 19:44:34-debug: [Build Memory track]: 查询 Asset Bundle start:200.83MB, end 200.20MB, increase: -648.78KB
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-debug:   Number of all scenes: 3
2025-7-16 19:44:34-debug:   Number of other assets: 405
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-16 19:44:34-debug: [Build Memory track]: 查询 Asset Bundle start:200.23MB, end 200.74MB, increase: 522.09KB
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 19:44:34-debug: Init bundle root assets success..., progress: 0%
2025-7-16 19:44:34-debug:   Number of all scripts: 24
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-16 19:44:34-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 19:44:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.77MB, end 200.10MB, increase: -687.48KB
2025-7-16 19:44:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 19:44:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-16 19:44:34-debug: [Build Memory track]: 查询 Asset Bundle start:200.08MB, end 200.16MB, increase: 73.62KB
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:200.14MB, end 200.42MB, increase: 288.92KB
2025-7-16 19:44:34-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 19:44:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.45MB, end 200.31MB, increase: -145.72KB
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 19:44:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 19:44:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 19:44:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 19:44:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 19:44:34-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-16 19:44:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:200.44MB, end 200.48MB, increase: 37.73KB
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 19:44:34-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 19:44:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 19:44:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 19:44:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.59MB, end 200.61MB, increase: 27.50KB
2025-7-16 19:44:34-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-16 19:44:34-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-16 19:44:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:200.65MB, end 200.68MB, increase: 32.36KB
2025-7-16 19:44:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 19:44:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-16 19:44:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.71MB, end 200.21MB, increase: -511.11KB
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-16 19:44:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-16 19:44:55-debug: Query all assets info in project
2025-7-16 19:44:55-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 19:44:55-debug: Skip compress image, progress: 0%
2025-7-16 19:44:55-debug: Init all bundles start..., progress: 0%
2025-7-16 19:44:55-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 19:44:55-debug: Num of bundles: 3..., progress: 0%
2025-7-16 19:44:55-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:55-debug: Init bundle root assets start..., progress: 0%
2025-7-16 19:44:55-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 19:44:55-debug: Init bundle root assets success..., progress: 0%
2025-7-16 19:44:55-debug:   Number of all scenes: 3
2025-7-16 19:44:55-debug:   Number of all scripts: 24
2025-7-16 19:44:55-debug:   Number of other assets: 405
2025-7-16 19:44:55-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-16 19:44:55-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:44:55-debug: [Build Memory track]: 查询 Asset Bundle start:201.09MB, end 201.77MB, increase: 698.18KB
2025-7-16 19:44:55-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 19:44:55-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-16 19:44:55-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-16 19:44:55-debug: [Build Memory track]: 查询 Asset Bundle start:201.80MB, end 200.40MB, increase: -1438.18KB
2025-7-16 19:44:55-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 19:44:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:55-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-7-16 19:44:55-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 19:44:55-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 19:44:55-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.43MB, end 200.45MB, increase: 18.62KB
2025-7-16 19:44:55-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 19:44:55-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-16 19:44:55-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 19:44:55-debug: [Build Memory track]: 填充脚本数据到 settings.json start:200.48MB, end 200.50MB, increase: 17.72KB
2025-7-16 19:44:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:44:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-16 19:44:55-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.53MB, end 200.66MB, increase: 129.73KB
2025-7-16 19:44:55-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-16 19:47:19-debug: refresh db internal success
2025-7-16 19:47:19-debug: refresh db assets success
2025-7-16 19:47:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:47:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:47:19-debug: asset-db:refresh-all-database (24ms)
2025-7-16 19:47:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:47:20-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:47:20-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-16 19:50:34-debug: refresh db internal success
2025-7-16 19:50:34-debug: refresh db assets success
2025-7-16 19:50:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:50:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:50:34-debug: asset-db:refresh-all-database (31ms)
2025-7-16 19:50:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:50:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:52:11-debug: refresh db internal success
2025-7-16 19:52:11-debug: refresh db assets success
2025-7-16 19:52:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:52:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:52:11-debug: asset-db:refresh-all-database (37ms)
2025-7-16 19:52:39-debug: refresh db internal success
2025-7-16 19:52:39-debug: refresh db assets success
2025-7-16 19:52:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:52:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:52:39-debug: asset-db:refresh-all-database (35ms)
2025-7-16 19:52:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:52:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:53:24-debug: refresh db internal success
2025-7-16 19:53:24-debug: refresh db assets success
2025-7-16 19:53:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:53:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:53:24-debug: asset-db:refresh-all-database (18ms)
2025-7-16 19:53:25-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/settings.png...
2025-7-16 19:53:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/settings.png
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:53:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/settings.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:53:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/settings.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:53:25-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-16 19:53:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:53:35-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:53:35-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-16 19:53:36-debug: Query all assets info in project
2025-7-16 19:53:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 19:53:36-debug: Skip compress image, progress: 0%
2025-7-16 19:53:36-debug: Init all bundles start..., progress: 0%
2025-7-16 19:53:36-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:53:36-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 19:53:36-debug: Num of bundles: 3..., progress: 0%
2025-7-16 19:53:36-debug: Init bundle root assets start..., progress: 0%
2025-7-16 19:53:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 19:53:36-debug:   Number of all scenes: 3
2025-7-16 19:53:36-debug:   Number of all scripts: 24
2025-7-16 19:53:36-debug:   Number of other assets: 408
2025-7-16 19:53:36-debug: Init bundle root assets success..., progress: 0%
2025-7-16 19:53:36-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-16 19:53:36-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 19:53:36-debug: [Build Memory track]: 查询 Asset Bundle start:196.95MB, end 196.90MB, increase: -45.96KB
2025-7-16 19:53:36-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 19:53:36-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-16 19:53:36-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-16 19:53:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 19:53:36-debug: [Build Memory track]: 查询 Asset Bundle start:196.93MB, end 197.03MB, increase: 100.73KB
2025-7-16 19:53:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:53:36-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-7-16 19:53:36-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 19:53:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:197.06MB, end 197.09MB, increase: 25.13KB
2025-7-16 19:53:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 19:53:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 19:53:36-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-7-16 19:53:36-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-7-16 19:53:36-debug: [Build Memory track]: 填充脚本数据到 settings.json start:197.12MB, end 197.15MB, increase: 29.54KB
2025-7-16 19:53:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 19:53:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 19:53:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-16 19:53:36-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-16 19:53:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:197.18MB, end 197.31MB, increase: 133.48KB
2025-7-16 19:54:16-debug: refresh db internal success
2025-7-16 19:54:16-debug: refresh db assets success
2025-7-16 19:54:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:54:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:54:16-debug: asset-db:refresh-all-database (39ms)
2025-7-16 19:54:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:54:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:54:24-debug: refresh db internal success
2025-7-16 19:54:24-debug: refresh db assets success
2025-7-16 19:54:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:54:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:54:24-debug: asset-db:refresh-all-database (28ms)
2025-7-16 19:54:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:54:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:54:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:54:26-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:54:26-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:54:26-debug: asset-db:reimport-asset/Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png (90ms)
2025-7-16 19:54:26-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png...
2025-7-16 19:54:26-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-16 19:54:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:54:30-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:54:30-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:54:30-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:54:30-debug: asset-db:reimport-asset593904f2-fa85-498f-9b0e-9f79890c24fd (35ms)
2025-7-16 19:54:42-debug: refresh db internal success
2025-7-16 19:54:42-debug: refresh db assets success
2025-7-16 19:54:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:54:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:54:42-debug: asset-db:refresh-all-database (27ms)
2025-7-16 19:54:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:54:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:55:58-debug: refresh db internal success
2025-7-16 19:55:58-debug: refresh db assets success
2025-7-16 19:55:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:55:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:55:58-debug: asset-db:refresh-all-database (39ms)
2025-7-16 19:55:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:55:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:56:05-debug: refresh db internal success
2025-7-16 19:56:05-debug: refresh db assets success
2025-7-16 19:56:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 19:56:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 19:56:05-debug: asset-db:refresh-all-database (26ms)
2025-7-16 19:56:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 19:56:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 19:56:06-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:56:06-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:56:06-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:56:06-debug: asset-db:reimport-asset/Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png (16ms)
2025-7-16 19:56:06-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/yellow.png...
2025-7-16 19:56:06-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-16 19:56:06-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:56:07-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 19:56:07-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-16 20:58:01-debug: refresh db internal success
2025-7-16 20:58:01-debug: refresh db assets success
2025-7-16 20:58:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 20:58:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 20:58:01-debug: asset-db:refresh-all-database (51ms)
2025-7-16 20:58:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 20:58:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 20:58:07-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png...
2025-7-16 20:58:07-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png
background: #aaff85; color: #000;
color: #000;
2025-7-16 20:58:07-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-16 20:58:07-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 20:58:07-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-16 20:58:07-debug: refresh db internal success
2025-7-16 20:58:07-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-16 20:58:07-debug: refresh db assets success
2025-7-16 20:58:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 20:58:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 20:58:07-debug: asset-db:refresh-all-database (15ms)
2025-7-16 20:58:19-debug: Query all assets info in project
2025-7-16 20:58:19-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 20:58:19-debug: Skip compress image, progress: 0%
2025-7-16 20:58:19-debug: Init all bundles start..., progress: 0%
2025-7-16 20:58:19-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 20:58:19-debug: Num of bundles: 3..., progress: 0%
2025-7-16 20:58:19-debug: Init bundle root assets start..., progress: 0%
2025-7-16 20:58:19-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 20:58:19-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 20:58:19-debug: Init bundle root assets success..., progress: 0%
2025-7-16 20:58:19-debug:   Number of other assets: 411
2025-7-16 20:58:19-debug:   Number of all scripts: 24
2025-7-16 20:58:19-debug:   Number of all scenes: 3
2025-7-16 20:58:19-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-16 20:58:19-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-16 20:58:19-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 20:58:19-debug: [Build Memory track]: 查询 Asset Bundle start:200.97MB, end 200.72MB, increase: -257.52KB
2025-7-16 20:58:19-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 20:58:19-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-16 20:58:19-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-7-16 20:58:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 20:58:19-debug: [Build Memory track]: 查询 Asset Bundle start:200.74MB, end 200.83MB, increase: 95.39KB
2025-7-16 20:58:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 20:58:19-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 20:58:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.86MB, end 200.87MB, increase: 15.27KB
2025-7-16 20:58:19-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 20:58:19-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 20:58:19-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-16 20:58:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 20:58:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 20:58:19-debug: [Build Memory track]: 填充脚本数据到 settings.json start:200.90MB, end 200.91MB, increase: 15.91KB
2025-7-16 20:58:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-7-16 20:58:19-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-7-16 20:58:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.94MB, end 201.08MB, increase: 140.79KB
2025-7-16 20:58:24-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 20:58:24-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-16 20:58:24-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 20:58:24-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (3ms)
2025-7-16 20:58:25-debug: Query all assets info in project
2025-7-16 20:58:25-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-16 20:58:25-debug: Skip compress image, progress: 0%
2025-7-16 20:58:25-debug: Init all bundles start..., progress: 0%
2025-7-16 20:58:25-debug: Num of bundles: 3..., progress: 0%
2025-7-16 20:58:25-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 20:58:25-debug: 查询 Asset Bundle start, progress: 0%
2025-7-16 20:58:25-debug: Init bundle root assets start..., progress: 0%
2025-7-16 20:58:25-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-16 20:58:25-debug: Init bundle root assets success..., progress: 0%
2025-7-16 20:58:25-debug:   Number of all scripts: 24
2025-7-16 20:58:25-debug:   Number of other assets: 411
2025-7-16 20:58:25-debug:   Number of all scenes: 3
2025-7-16 20:58:25-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-16 20:58:25-debug: // ---- build task 查询 Asset Bundle ----
2025-7-16 20:58:25-debug: [Build Memory track]: 查询 Asset Bundle start:201.73MB, end 201.24MB, increase: -505.13KB
2025-7-16 20:58:25-debug: 查询 Asset Bundle start, progress: 5%
2025-7-16 20:58:25-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-16 20:58:25-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-16 20:58:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-16 20:58:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 20:58:25-debug: [Build Memory track]: 查询 Asset Bundle start:201.27MB, end 201.36MB, increase: 94.73KB
2025-7-16 20:58:25-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-16 20:58:25-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-16 20:58:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:201.38MB, end 201.40MB, increase: 15.75KB
2025-7-16 20:58:25-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-16 20:58:25-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-16 20:58:25-debug: [Build Memory track]: 填充脚本数据到 settings.json start:201.42MB, end 201.44MB, increase: 15.12KB
2025-7-16 20:58:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-16 20:58:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-16 20:58:25-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-7-16 20:58:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:201.46MB, end 201.58MB, increase: 120.13KB
2025-7-16 20:58:37-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 20:58:37-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-16 21:00:07-debug: refresh db internal success
2025-7-16 21:00:07-debug: refresh db assets success
2025-7-16 21:00:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:00:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:00:07-debug: asset-db:refresh-all-database (38ms)
2025-7-16 21:00:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:00:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:00:49-debug: refresh db internal success
2025-7-16 21:00:49-debug: refresh db assets success
2025-7-16 21:00:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:00:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:00:49-debug: asset-db:refresh-all-database (33ms)
2025-7-16 21:00:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:00:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:01:56-debug: refresh db internal success
2025-7-16 21:01:56-debug: refresh db assets success
2025-7-16 21:01:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:01:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:01:56-debug: asset-db:refresh-all-database (35ms)
2025-7-16 21:01:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:02:31-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:02:31-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-16 21:02:42-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:02:42-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-16 21:04:46-debug: refresh db internal success
2025-7-16 21:04:46-debug: refresh db assets success
2025-7-16 21:04:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:04:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:04:46-debug: asset-db:refresh-all-database (42ms)
2025-7-16 21:04:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:04:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:04:52-debug: refresh db internal success
2025-7-16 21:04:52-debug: refresh db assets success
2025-7-16 21:04:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:04:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:04:52-debug: asset-db:refresh-all-database (24ms)
2025-7-16 21:04:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:05:04-debug: refresh db internal success
2025-7-16 21:05:04-debug: refresh db assets success
2025-7-16 21:05:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:05:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:05:04-debug: asset-db:refresh-all-database (33ms)
2025-7-16 21:05:40-debug: refresh db internal success
2025-7-16 21:05:40-debug: refresh db assets success
2025-7-16 21:05:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:05:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:05:40-debug: asset-db:refresh-all-database (33ms)
2025-7-16 21:05:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:05:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:05:58-debug: refresh db internal success
2025-7-16 21:05:58-debug: refresh db assets success
2025-7-16 21:05:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:05:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:05:58-debug: asset-db:refresh-all-database (30ms)
2025-7-16 21:05:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:05:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:06:44-debug: refresh db internal success
2025-7-16 21:06:44-debug: refresh db assets success
2025-7-16 21:06:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:06:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:06:44-debug: asset-db:refresh-all-database (31ms)
2025-7-16 21:06:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:06:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:07:24-debug: refresh db internal success
2025-7-16 21:07:24-debug: refresh db assets success
2025-7-16 21:07:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:07:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:07:24-debug: asset-db:refresh-all-database (23ms)
2025-7-16 21:07:31-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:07:31-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-16 21:07:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:07:48-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-16 21:08:08-debug: refresh db internal success
2025-7-16 21:08:08-debug: refresh db assets success
2025-7-16 21:08:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:08:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:08:08-debug: asset-db:refresh-all-database (40ms)
2025-7-16 21:08:24-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:08:24-debug: asset-db:reimport-assetc5508b8c-cf95-42d0-8edb-f7f4504d41df@f9941 (14ms)
2025-7-16 21:08:33-debug: %cReImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:08:33-debug: asset-db:reimport-assetc5508b8c-cf95-42d0-8edb-f7f4504d41df@f9941 (27ms)
2025-7-16 21:09:08-debug: refresh db internal success
2025-7-16 21:09:08-debug: refresh db assets success
2025-7-16 21:09:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:09:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:09:08-debug: asset-db:refresh-all-database (44ms)
2025-7-16 21:09:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:09:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:09:53-debug: refresh db internal success
2025-7-16 21:09:53-debug: refresh db assets success
2025-7-16 21:09:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:09:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:09:53-debug: asset-db:refresh-all-database (27ms)
2025-7-16 21:09:57-debug: refresh db internal success
2025-7-16 21:09:57-debug: refresh db assets success
2025-7-16 21:09:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:09:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:09:57-debug: asset-db:refresh-all-database (18ms)
2025-7-16 21:09:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:09:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:10:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:10:26-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-16 21:10:40-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:10:40-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-16 21:11:02-debug: refresh db internal success
2025-7-16 21:11:02-debug: refresh db assets success
2025-7-16 21:11:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:11:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:11:02-debug: asset-db:refresh-all-database (19ms)
2025-7-16 21:11:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:11:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:11:10-debug: refresh db internal success
2025-7-16 21:11:10-debug: refresh db assets success
2025-7-16 21:11:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:11:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:11:10-debug: asset-db:refresh-all-database (31ms)
2025-7-16 21:11:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:11:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:11:30-debug: refresh db internal success
2025-7-16 21:11:30-debug: refresh db assets success
2025-7-16 21:11:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:11:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:11:30-debug: asset-db:refresh-all-database (33ms)
2025-7-16 21:11:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:11:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:11:45-debug: refresh db internal success
2025-7-16 21:11:45-debug: refresh db assets success
2025-7-16 21:11:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:11:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:11:45-debug: asset-db:refresh-all-database (19ms)
2025-7-16 21:11:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:11:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:11:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:11:48-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-16 21:11:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:11:50-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (3ms)
2025-7-16 21:12:07-debug: refresh db internal success
2025-7-16 21:12:07-debug: refresh db assets success
2025-7-16 21:12:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:12:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:12:07-debug: asset-db:refresh-all-database (22ms)
2025-7-16 21:12:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:12:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:12:42-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:12:42-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-16 21:12:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:12:48-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (3ms)
2025-7-16 21:12:57-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:12:57-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (7ms)
2025-7-16 21:13:22-debug: refresh db internal success
2025-7-16 21:13:22-debug: refresh db assets success
2025-7-16 21:13:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:13:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:13:22-debug: asset-db:refresh-all-database (26ms)
2025-7-16 21:14:02-debug: refresh db internal success
2025-7-16 21:14:02-debug: refresh db assets success
2025-7-16 21:14:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:14:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:14:02-debug: asset-db:refresh-all-database (26ms)
2025-7-16 21:14:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:14:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:14:10-debug: refresh db internal success
2025-7-16 21:14:10-debug: refresh db assets success
2025-7-16 21:14:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:14:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:14:10-debug: asset-db:refresh-all-database (30ms)
2025-7-16 21:14:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:14:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:15:05-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:15:05-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-16 21:18:26-debug: refresh db internal success
2025-7-16 21:18:26-debug: refresh db assets success
2025-7-16 21:18:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:18:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:18:26-debug: asset-db:refresh-all-database (37ms)
2025-7-16 21:18:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:18:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:18:51-debug: refresh db internal success
2025-7-16 21:18:51-debug: refresh db assets success
2025-7-16 21:18:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:18:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:18:51-debug: asset-db:refresh-all-database (27ms)
2025-7-16 21:19:01-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:19:01-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (3ms)
2025-7-16 21:19:25-debug: refresh db internal success
2025-7-16 21:19:25-debug: refresh db assets success
2025-7-16 21:19:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:19:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:19:25-debug: asset-db:refresh-all-database (43ms)
2025-7-16 21:23:30-debug: refresh db internal success
2025-7-16 21:23:30-debug: refresh db assets success
2025-7-16 21:23:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:23:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:23:30-debug: asset-db:refresh-all-database (37ms)
2025-7-16 21:23:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:23:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:24:21-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel2.png
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:21-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel2.png...
2025-7-16 21:24:21-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:21-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:21-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-16 21:24:21-debug: refresh db internal success
2025-7-16 21:24:21-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:21-debug: refresh db assets success
2025-7-16 21:24:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:24:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:24:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-16 21:24:21-debug: asset-db:refresh-all-database (55ms)
2025-7-16 21:24:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 21:24:25-debug: programming:execute-script (2ms)
2025-7-16 21:24:26-debug: start remove asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png...
2025-7-16 21:24:26-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png...
2025-7-16 21:24:26-debug: %cDestroy%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-7-16 21:24:26-debug: %cDestroy%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-7-16 21:24:26-debug: %cDestroy%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png
background: #ffb8b8; color: #000;
color: #000;
2025-7-16 21:24:26-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-16 21:24:26-debug: remove asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png success
2025-7-16 21:24:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:29-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png...
2025-7-16 21:24:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/panel.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:29-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-16 21:24:29-debug: refresh db internal success
2025-7-16 21:24:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:29-debug: refresh db assets success
2025-7-16 21:24:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 21:24:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 21:24:29-debug: asset-db:refresh-all-database (13ms)
2025-7-16 21:24:54-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:54-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (7ms)
2025-7-16 21:24:56-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 21:24:56-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-16 22:10:33-debug: refresh db internal success
2025-7-16 22:10:33-debug: refresh db assets success
2025-7-16 22:10:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-16 22:10:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-16 22:10:33-debug: asset-db:refresh-all-database (47ms)
2025-7-16 22:10:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-16 22:10:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-16 22:10:49-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:10:49-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-16 22:10:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:10:50-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-16 22:10:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:10:50-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (7ms)
2025-7-16 22:10:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:10:50-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-16 22:10:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:10:50-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-16 22:10:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:10:50-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-16 22:10:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:10:51-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (18ms)
2025-7-16 22:11:04-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:11:04-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (14ms)
2025-7-16 22:12:07-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:12:07-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (10ms)
2025-7-16 22:12:09-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-16 22:12:09-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-17 12:55:17-debug: refresh db internal success
2025-7-17 12:55:17-debug: refresh db assets success
2025-7-17 12:55:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 12:55:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 12:55:17-debug: asset-db:refresh-all-database (39ms)
2025-7-17 12:55:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 12:55:48-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/purchase.png...
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/purchase.png
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/restart.png...
2025-7-17 12:55:48-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/back.png...
2025-7-17 12:55:48-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/close.png...
2025-7-17 12:55:48-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/select.png...
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/purchase.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/purchase.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/restart.png
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/restart.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/restart.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/back.png
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/back.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/back.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/close.png
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/close.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/close.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: refresh db internal success
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/select.png
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/select.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/select.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-7-17 12:55:48-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-7-17 12:55:48-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-17 12:55:48-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-7-17 12:55:48-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-17 12:55:48-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-7-17 12:55:48-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-17 12:55:48-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-17 12:55:48-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-17 12:55:48-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-7-17 12:55:48-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:55:48-debug: refresh db assets success
2025-7-17 12:55:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 12:55:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 12:55:48-debug: asset-db:refresh-all-database (60ms)
2025-7-17 12:55:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 12:55:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 12:56:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:56:29-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-17 12:57:21-debug: refresh db internal success
2025-7-17 12:57:21-debug: refresh db assets success
2025-7-17 12:57:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 12:57:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 12:57:21-debug: asset-db:refresh-all-database (30ms)
2025-7-17 12:57:33-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:57:33-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-17 12:58:53-debug: refresh db internal success
2025-7-17 12:58:53-debug: refresh db assets success
2025-7-17 12:58:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 12:58:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 12:58:53-debug: asset-db:refresh-all-database (25ms)
2025-7-17 12:58:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 12:58:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 12:59:55-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 12:59:55-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (8ms)
2025-7-17 13:00:12-debug: refresh db internal success
2025-7-17 13:00:12-debug: refresh db assets success
2025-7-17 13:00:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 13:00:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 13:00:12-debug: asset-db:refresh-all-database (40ms)
2025-7-17 13:00:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 13:00:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 13:00:54-debug: refresh db internal success
2025-7-17 13:00:54-debug: refresh db assets success
2025-7-17 13:00:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 13:00:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 13:00:54-debug: asset-db:refresh-all-database (31ms)
2025-7-17 13:00:55-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 13:00:55-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (9ms)
2025-7-17 13:01:17-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 13:01:17-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-17 13:01:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 13:01:27-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (7ms)
2025-7-17 19:04:44-debug: refresh db internal success
2025-7-17 19:04:44-debug: refresh db assets success
2025-7-17 19:04:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 19:04:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 19:04:44-debug: asset-db:refresh-all-database (58ms)
2025-7-17 19:04:44-debug: asset-db:worker-effect-data-processing (4ms)
2025-7-17 19:04:44-debug: asset-db-hook-engine-extends-afterRefresh (7ms)
2025-7-17 20:41:57-debug: refresh db internal success
2025-7-17 20:41:57-debug: refresh db assets success
2025-7-17 20:41:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 20:41:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 20:41:57-debug: asset-db:refresh-all-database (32ms)
2025-7-17 20:41:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 20:41:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-17 20:42:28-debug: refresh db internal success
2025-7-17 20:42:28-debug: refresh db assets success
2025-7-17 20:42:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 20:42:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 20:42:28-debug: asset-db:refresh-all-database (53ms)
2025-7-17 20:42:28-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 20:42:28-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (10ms)
2025-7-17 21:13:23-debug: refresh db internal success
2025-7-17 21:13:23-debug: refresh db assets success
2025-7-17 21:13:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 21:13:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 21:13:23-debug: asset-db:refresh-all-database (43ms)
2025-7-17 21:13:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 21:13:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 21:13:53-debug: refresh db internal success
2025-7-17 21:13:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 21:13:53-debug: refresh db assets success
2025-7-17 21:13:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 21:13:53-debug: asset-db:refresh-all-database (30ms)
2025-7-17 21:17:43-debug: refresh db internal success
2025-7-17 21:17:43-debug: refresh db assets success
2025-7-17 21:17:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 21:17:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 21:17:43-debug: asset-db:refresh-all-database (42ms)
2025-7-17 21:17:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 21:17:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 21:19:00-debug: refresh db internal success
2025-7-17 21:19:00-debug: refresh db assets success
2025-7-17 21:19:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 21:19:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 21:19:00-debug: asset-db:refresh-all-database (25ms)
2025-7-17 21:19:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 21:19:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 21:20:44-debug: start refresh asset from /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/setting.png...
2025-7-17 21:20:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/setting.png
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:20:44-debug: asset-db:reimport-asset/Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/setting.png (1ms)
2025-7-17 21:20:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/setting.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:20:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu/setting.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:20:44-debug: refresh asset /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu success
2025-7-17 21:20:44-debug: refresh db internal success
2025-7-17 21:20:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/img/menu
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:20:44-debug: refresh db assets success
2025-7-17 21:20:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 21:20:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 21:20:44-debug: asset-db:refresh-all-database (14ms)
2025-7-17 21:20:59-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:20:59-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (7ms)
2025-7-17 21:21:11-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:21:11-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-17 21:21:15-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:21:15-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-17 21:21:21-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 21:21:21-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (4ms)
2025-7-17 21:35:42-debug: refresh db internal success
2025-7-17 21:35:42-debug: refresh db assets success
2025-7-17 21:35:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 21:35:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 21:35:42-debug: asset-db:refresh-all-database (41ms)
2025-7-17 21:35:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 21:35:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-17 22:01:23-debug: refresh db internal success
2025-7-17 22:01:23-debug: refresh db assets success
2025-7-17 22:01:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:01:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:01:23-debug: asset-db:refresh-all-database (38ms)
2025-7-17 22:01:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:01:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:03:03-debug: refresh db internal success
2025-7-17 22:03:03-debug: refresh db assets success
2025-7-17 22:03:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:03:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:03:03-debug: asset-db:refresh-all-database (31ms)
2025-7-17 22:03:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:03:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:11:30-debug: refresh db internal success
2025-7-17 22:11:30-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:11:30-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:11:30-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:11:30-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:11:30-debug: refresh db assets success
2025-7-17 22:11:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:11:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:11:30-debug: asset-db:refresh-all-database (39ms)
2025-7-17 22:11:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:11:30-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-17 22:11:58-debug: refresh db internal success
2025-7-17 22:11:58-debug: refresh db assets success
2025-7-17 22:11:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:11:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:11:58-debug: asset-db:refresh-all-database (28ms)
2025-7-17 22:11:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:11:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:12:21-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:12:21-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (5ms)
2025-7-17 22:12:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:12:27-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (3ms)
2025-7-17 22:12:27-debug: Query all assets info in project
2025-7-17 22:12:27-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-17 22:12:27-debug: Skip compress image, progress: 0%
2025-7-17 22:12:27-debug: Init all bundles start..., progress: 0%
2025-7-17 22:12:27-debug: Num of bundles: 3..., progress: 0%
2025-7-17 22:12:27-debug: 查询 Asset Bundle start, progress: 0%
2025-7-17 22:12:27-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:12:27-debug: Init bundle root assets start..., progress: 0%
2025-7-17 22:12:27-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-17 22:12:27-debug:   Number of all scenes: 3
2025-7-17 22:12:27-debug:   Number of all scripts: 25
2025-7-17 22:12:27-debug: Init bundle root assets success..., progress: 0%
2025-7-17 22:12:27-debug:   Number of other assets: 432
2025-7-17 22:12:27-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-17 22:12:27-debug: [Build Memory track]: 查询 Asset Bundle start:201.92MB, end 201.91MB, increase: -15.78KB
2025-7-17 22:12:27-debug: 查询 Asset Bundle start, progress: 5%
2025-7-17 22:12:27-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:12:27-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-17 22:12:27-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-17 22:12:27-debug: [Build Memory track]: 查询 Asset Bundle start:201.93MB, end 202.02MB, increase: 90.24KB
2025-7-17 22:12:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-17 22:12:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:12:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-17 22:12:27-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-17 22:12:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:202.05MB, end 202.07MB, increase: 23.81KB
2025-7-17 22:12:27-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-17 22:12:27-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-17 22:12:27-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-17 22:12:27-debug: [Build Memory track]: 填充脚本数据到 settings.json start:202.09MB, end 202.11MB, increase: 16.66KB
2025-7-17 22:12:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-17 22:12:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:12:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-17 22:12:27-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-17 22:12:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:202.13MB, end 202.26MB, increase: 132.91KB
2025-7-17 22:12:44-debug: refresh db internal success
2025-7-17 22:12:44-debug: refresh db assets success
2025-7-17 22:12:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:12:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:12:44-debug: asset-db:refresh-all-database (30ms)
2025-7-17 22:12:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:12:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:12:45-debug: Query all assets info in project
2025-7-17 22:12:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-17 22:12:45-debug: Skip compress image, progress: 0%
2025-7-17 22:12:45-debug: Init all bundles start..., progress: 0%
2025-7-17 22:12:45-debug: Num of bundles: 3..., progress: 0%
2025-7-17 22:12:45-debug: 查询 Asset Bundle start, progress: 0%
2025-7-17 22:12:45-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:12:45-debug: Init bundle root assets start..., progress: 0%
2025-7-17 22:12:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-17 22:12:45-debug: Init bundle root assets success..., progress: 0%
2025-7-17 22:12:45-debug:   Number of all scripts: 25
2025-7-17 22:12:45-debug:   Number of other assets: 432
2025-7-17 22:12:45-debug:   Number of all scenes: 3
2025-7-17 22:12:45-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-17 22:12:45-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-17 22:12:45-debug: [Build Memory track]: 查询 Asset Bundle start:203.03MB, end 202.80MB, increase: -233.25KB
2025-7-17 22:12:45-debug: 查询 Asset Bundle start, progress: 5%
2025-7-17 22:12:45-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:12:45-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-17 22:12:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-17 22:12:45-debug: [Build Memory track]: 查询 Asset Bundle start:202.83MB, end 202.92MB, increase: 90.22KB
2025-7-17 22:12:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:12:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-17 22:12:45-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-17 22:12:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:202.94MB, end 202.96MB, increase: 23.81KB
2025-7-17 22:12:45-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-17 22:12:45-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-7-17 22:12:45-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-17 22:12:45-debug: [Build Memory track]: 填充脚本数据到 settings.json start:202.99MB, end 203.01MB, increase: 16.48KB
2025-7-17 22:12:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-17 22:12:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:12:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-17 22:12:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.03MB, end 203.16MB, increase: 136.62KB
2025-7-17 22:12:45-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-17 22:13:09-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:13:09-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (6ms)
2025-7-17 22:13:10-debug: Query all assets info in project
2025-7-17 22:13:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-17 22:13:10-debug: Skip compress image, progress: 0%
2025-7-17 22:13:10-debug: Init all bundles start..., progress: 0%
2025-7-17 22:13:10-debug: 查询 Asset Bundle start, progress: 0%
2025-7-17 22:13:10-debug: Num of bundles: 3..., progress: 0%
2025-7-17 22:13:10-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:13:10-debug: Init bundle root assets start..., progress: 0%
2025-7-17 22:13:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-17 22:13:10-debug:   Number of all scenes: 3
2025-7-17 22:13:10-debug:   Number of all scripts: 25
2025-7-17 22:13:10-debug: Init bundle root assets success..., progress: 0%
2025-7-17 22:13:10-debug:   Number of other assets: 432
2025-7-17 22:13:10-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-17 22:13:10-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-17 22:13:10-debug: 查询 Asset Bundle start, progress: 5%
2025-7-17 22:13:10-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:13:10-debug: [Build Memory track]: 查询 Asset Bundle start:203.09MB, end 202.97MB, increase: -120.14KB
2025-7-17 22:13:10-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-7-17 22:13:10-debug: [Build Memory track]: 查询 Asset Bundle start:202.99MB, end 203.08MB, increase: 92.17KB
2025-7-17 22:13:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-17 22:13:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:13:10-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-17 22:13:10-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-17 22:13:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-17 22:13:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.11MB, end 203.12MB, increase: 15.95KB
2025-7-17 22:13:10-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-17 22:13:10-debug: [Build Memory track]: 填充脚本数据到 settings.json start:203.15MB, end 203.16MB, increase: 15.53KB
2025-7-17 22:13:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-17 22:13:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:13:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-7-17 22:13:10-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-7-17 22:13:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.19MB, end 203.32MB, increase: 130.28KB
2025-7-17 22:13:34-debug: refresh db internal success
2025-7-17 22:13:34-debug: refresh db assets success
2025-7-17 22:13:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:13:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:13:34-debug: asset-db:refresh-all-database (36ms)
2025-7-17 22:13:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:13:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:14:04-debug: refresh db internal success
2025-7-17 22:14:04-debug: refresh db assets success
2025-7-17 22:14:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:14:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:14:04-debug: asset-db:refresh-all-database (21ms)
2025-7-17 22:14:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:14:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:15:41-debug: refresh db internal success
2025-7-17 22:15:41-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:15:41-debug: refresh db assets success
2025-7-17 22:15:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:15:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:15:41-debug: asset-db:refresh-all-database (39ms)
2025-7-17 22:15:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:15:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:15:42-debug: Query all assets info in project
2025-7-17 22:15:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-17 22:15:42-debug: Skip compress image, progress: 0%
2025-7-17 22:15:42-debug: Init all bundles start..., progress: 0%
2025-7-17 22:15:42-debug: Num of bundles: 3..., progress: 0%
2025-7-17 22:15:42-debug: 查询 Asset Bundle start, progress: 0%
2025-7-17 22:15:42-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:15:42-debug: Init bundle root assets start..., progress: 0%
2025-7-17 22:15:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-17 22:15:42-debug:   Number of other assets: 432
2025-7-17 22:15:42-debug:   Number of all scripts: 25
2025-7-17 22:15:42-debug:   Number of all scenes: 3
2025-7-17 22:15:42-debug: Init bundle root assets success..., progress: 0%
2025-7-17 22:15:42-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-7-17 22:15:42-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-7-17 22:15:42-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:15:42-debug: [Build Memory track]: 查询 Asset Bundle start:204.47MB, end 205.18MB, increase: 719.87KB
2025-7-17 22:15:42-debug: 查询 Asset Bundle start, progress: 5%
2025-7-17 22:15:42-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-17 22:15:42-debug: [Build Memory track]: 查询 Asset Bundle start:205.20MB, end 204.54MB, increase: -673.09KB
2025-7-17 22:15:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-17 22:15:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:15:42-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-7-17 22:15:42-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-17 22:15:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.57MB, end 204.59MB, increase: 16.78KB
2025-7-17 22:15:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-17 22:15:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-17 22:15:42-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-17 22:15:42-debug: [Build Memory track]: 填充脚本数据到 settings.json start:204.61MB, end 204.63MB, increase: 15.23KB
2025-7-17 22:15:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:15:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-17 22:15:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-7-17 22:15:42-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-7-17 22:15:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.65MB, end 204.77MB, increase: 122.68KB
2025-7-17 22:16:45-debug: refresh db internal success
2025-7-17 22:16:45-debug: %cDestroy%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-17 22:16:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:16:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:16:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:16:45-debug: refresh db assets success
2025-7-17 22:16:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:16:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:16:45-debug: asset-db:refresh-all-database (46ms)
2025-7-17 22:16:45-debug: asset-db:worker-effect-data-processing (4ms)
2025-7-17 22:16:45-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-7-17 22:18:11-debug: refresh db internal success
2025-7-17 22:18:11-debug: refresh db assets success
2025-7-17 22:18:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:18:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:18:11-debug: asset-db:refresh-all-database (30ms)
2025-7-17 22:18:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:18:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:19:49-debug: refresh db internal success
2025-7-17 22:19:49-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:19:49-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:19:49-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:19:49-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:19:49-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:19:49-debug: refresh db assets success
2025-7-17 22:19:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:19:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:19:49-debug: asset-db:refresh-all-database (47ms)
2025-7-17 22:19:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:19:49-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-17 22:23:00-debug: refresh db internal success
2025-7-17 22:23:00-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFaderTest.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:23:00-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:23:00-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:23:00-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:23:00-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:23:00-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:23:00-debug: refresh db assets success
2025-7-17 22:23:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:23:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:23:00-debug: asset-db:refresh-all-database (49ms)
2025-7-17 22:23:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-17 22:23:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-17 22:23:13-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-7-17 22:23:13-debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (10ms)
2025-7-17 22:23:14-debug: Query all assets info in project
2025-7-17 22:23:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-17 22:23:14-debug: Skip compress image, progress: 0%
2025-7-17 22:23:14-debug: Init all bundles start..., progress: 0%
2025-7-17 22:23:14-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:23:14-debug: 查询 Asset Bundle start, progress: 0%
2025-7-17 22:23:14-debug: Init bundle root assets start..., progress: 0%
2025-7-17 22:23:14-debug: Num of bundles: 3..., progress: 0%
2025-7-17 22:23:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-17 22:23:14-debug: Init bundle root assets success..., progress: 0%
2025-7-17 22:23:14-debug:   Number of all scenes: 3
2025-7-17 22:23:14-debug:   Number of all scripts: 26
2025-7-17 22:23:14-debug:   Number of other assets: 432
2025-7-17 22:23:14-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-17 22:23:14-debug: [Build Memory track]: 查询 Asset Bundle start:207.17MB, end 207.11MB, increase: -58.96KB
2025-7-17 22:23:14-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-7-17 22:23:14-debug: // ---- build task 查询 Asset Bundle ----
2025-7-17 22:23:14-debug: 查询 Asset Bundle start, progress: 5%
2025-7-17 22:23:14-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-7-17 22:23:14-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-7-17 22:23:14-debug: [Build Memory track]: 查询 Asset Bundle start:207.14MB, end 207.23MB, increase: 98.01KB
2025-7-17 22:23:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-7-17 22:23:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:23:14-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-7-17 22:23:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-7-17 22:23:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-7-17 22:23:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.26MB, end 207.27MB, increase: 16.68KB
2025-7-17 22:23:14-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-7-17 22:23:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.30MB, end 207.32MB, increase: 15.50KB
2025-7-17 22:23:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-7-17 22:23:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-7-17 22:23:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-7-17 22:23:14-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-7-17 22:23:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.34MB, end 207.46MB, increase: 122.40KB
2025-7-17 22:23:24-debug: refresh db internal success
2025-7-17 22:23:24-debug: refresh db assets success
2025-7-17 22:23:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-17 22:23:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-17 22:23:24-debug: asset-db:refresh-all-database (33ms)
2025-7-17 22:23:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
