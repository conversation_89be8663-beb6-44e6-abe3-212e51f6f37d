7-16-2025 19:42:16 - log: Load engine in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine
7-16-2025 19:42:16 - log: Register native engine in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
7-16-2025 19:42:16 - log: Request namespace: device-list
7-16-2025 19:42:20 - info: [PreviewInEditor] 预览环境初始化完毕
7-16-2025 19:42:20 - log: [Scene] meshopt wasm decoder initialized
7-16-2025 19:42:20 - log: [Scene] [box2d]:box2d wasm lib loaded.
7-16-2025 19:42:20 - log: [Scene] [bullet]:bullet wasm lib loaded.
7-16-2025 19:42:20 - log: [Scene] [PHYSICS]: using builtin.
7-16-2025 19:42:20 - log: [Scene] Cocos Creator v3.8.6
7-16-2025 19:42:20 - log: [Scene] Using custom pipeline: Builtin
7-16-2025 19:42:20 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-16-2025 19:44:34 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-16-2025 19:44:34 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-16-2025 19:44:34 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-16-2025 19:44:34 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-16-2025 19:44:34 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-16-2025 19:44:34 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-16-2025 19:44:34 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-16-2025 19:44:35 - log: [PreviewInEditor] 检测到普通游戏环境
7-16-2025 19:44:35 - log: [PreviewInEditor] 玩家数据保存成功
7-16-2025 19:44:35 - log: [PreviewInEditor] 玩家数据已重置
7-16-2025 19:44:35 - log: [PreviewInEditor] 玩家数据加载成功
7-16-2025 19:44:36 - log: [PreviewInEditor] 更新关卡显示
7-16-2025 19:44:36 - log: [PreviewInEditor] 关卡 level-1: 解锁状态 = true
7-16-2025 19:44:36 - log: [PreviewInEditor] 关卡 level-2: 解锁状态 = false
7-16-2025 19:44:36 - log: [PreviewInEditor] 关卡 level-3: 解锁状态 = false
7-16-2025 19:44:39 - info: [PreviewInEditor] 预览环境初始化完毕
7-16-2025 19:44:55 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-16-2025 19:44:55 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-16-2025 19:44:55 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-16-2025 19:44:55 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-16-2025 19:44:55 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-16-2025 19:44:55 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-16-2025 19:44:55 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-16-2025 19:44:55 - log: [PreviewInEditor] 检测到普通游戏环境
7-16-2025 19:44:55 - log: [PreviewInEditor] 玩家数据保存成功
7-16-2025 19:44:55 - log: [PreviewInEditor] 玩家数据已重置
7-16-2025 19:44:55 - log: [PreviewInEditor] 玩家数据加载成功
7-16-2025 19:47:19 - info: [PreviewInEditor] 预览环境初始化完毕
7-16-2025 19:53:36 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-16-2025 19:53:36 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-16-2025 19:53:36 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-16-2025 19:53:36 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-16-2025 19:53:36 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-16-2025 19:53:36 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-16-2025 19:53:36 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-16-2025 19:53:36 - log: [PreviewInEditor] 检测到普通游戏环境
7-16-2025 19:53:36 - log: [PreviewInEditor] 玩家数据保存成功
7-16-2025 19:53:36 - log: [PreviewInEditor] 玩家数据已重置
7-16-2025 19:53:36 - log: [PreviewInEditor] 玩家数据加载成功
7-16-2025 19:53:44 - info: [PreviewInEditor] 预览环境初始化完毕
7-16-2025 20:58:19 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-16-2025 20:58:19 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-16-2025 20:58:19 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-16-2025 20:58:19 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-16-2025 20:58:19 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-16-2025 20:58:19 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-16-2025 20:58:19 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-16-2025 20:58:20 - log: [PreviewInEditor] 检测到普通游戏环境
7-16-2025 20:58:20 - log: [PreviewInEditor] 玩家数据保存成功
7-16-2025 20:58:20 - log: [PreviewInEditor] 玩家数据已重置
7-16-2025 20:58:20 - log: [PreviewInEditor] 玩家数据加载成功
7-16-2025 20:58:23 - info: [PreviewInEditor] 预览环境初始化完毕
7-16-2025 20:58:25 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-16-2025 20:58:25 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-16-2025 20:58:25 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-16-2025 20:58:25 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-16-2025 20:58:25 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-16-2025 20:58:25 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-16-2025 20:58:25 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-16-2025 20:58:25 - log: [PreviewInEditor] 检测到普通游戏环境
7-16-2025 20:58:25 - log: [PreviewInEditor] 玩家数据保存成功
7-16-2025 20:58:25 - log: [PreviewInEditor] 玩家数据已重置
7-16-2025 20:58:25 - log: [PreviewInEditor] 玩家数据加载成功
7-16-2025 20:58:36 - info: [PreviewInEditor] 预览环境初始化完毕
7-16-2025 21:02:30 - warn: [Scene] Can not find environment map, disable IBL lightingError: [Scene] Can not find environment map, disable IBL lighting
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178301:12)
    at warnID (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178489:5)
    at SkyboxInfo.set envmap (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:61152:13)
    at SkyboxInfo.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/asset/asset-watcher.ccc:1:1091)
    at AssetDump.decode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/types/asset-dump.ccc:1:946)
    at _decodeByType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:4617)
    at decodePatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:6313)
    at decodePatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:6952)
    at decodeScene (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:3735)
    at SceneUndoCommand.applyData (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/scene.ccc:1:722)
    at SceneUndoCommand.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/scene.ccc:1:616)
    at SceneUndoCommand.perform (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/base.ccc:1:209)
    at SceneUndoManager.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/base.ccc:1:752)
    at SceneUndoManager.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/scene.ccc:1:3833)
    at GeneralSceneFacade.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:12889)
    at SceneFacadeManager.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:15411)
7-16-2025 21:02:30 - warn: [Scene] Can not find environment map, disable IBL lightingError: [Scene] Can not find environment map, disable IBL lighting
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178301:12)
    at warnID (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178489:5)
    at SkyboxInfo.set envmap (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:61152:13)
    at SkyboxInfo.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/asset/asset-watcher.ccc:1:1091)
    at AssetDump.decode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/types/asset-dump.ccc:1:946)
    at _decodeByType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:4617)
    at decodePatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:6313)
    at decodePatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:6952)
    at decodeScene (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:3735)
    at SceneUndoCommand.applyData (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/scene.ccc:1:722)
    at SceneUndoCommand.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/scene.ccc:1:616)
    at SceneUndoCommand.perform (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/base.ccc:1:209)
    at SceneUndoManager.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/base.ccc:1:752)
    at SceneUndoManager.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/undo/scene.ccc:1:3833)
    at GeneralSceneFacade.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:12889)
    at SceneFacadeManager.undo (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:15411)
7-16-2025 21:09:57 - error: [Scene] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.onLoad (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95168:16)
    at callOnLoadInTryCatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55211:125)
    at NodeActivator.activateComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55256:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58319:46)
    at NodeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:12439)
    at GeneralSceneFacade.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:11455)
    at SceneFacadeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:14033)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:09:57 - error: [Scene] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.onEnable (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95176:16)
    at eval (eval at tryCatchFunctor_EDITOR (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js), <anonymous>:4:10)
    at ComponentScheduler.enableComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:53242:19)
    at NodeActivator.activateComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55272:46)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58319:46)
    at NodeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:12439)
    at GeneralSceneFacade.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:11455)
    at SceneFacadeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:14033)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:10:00 - log: [Scene] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.set type [as type] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95056:18)
    at NumberDump.decode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/types/number-dump.ccc:1:197)
    at _decodeByType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:4617)
    at decodePatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:6064)
    at DumpUtil.restoreProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/index.ccc:1:847)
    at NodeManager.setProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:8080)
    at GeneralSceneFacade.setNodeProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:7102)
    at SceneFacadeManager.setNodeProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:10632)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:10:00 - error: [Window] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at <process:scene>
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.set type [as type] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95056:18)
    at NumberDump.decode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/types/number-dump.ccc:1:197)
    at _decodeByType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:4617)
    at decodePatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/decode.ccc:1:6064)
    at DumpUtil.restoreProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/export/dump/index.ccc:1:847)
    at NodeManager.setProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:8080)
    at GeneralSceneFacade.setNodeProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:7102)
    at SceneFacadeManager.setNodeProperty (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:10632)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:10:33 - warn: [Scene] Remove Component failed: ebdiDBBPpIcYTyeWd4HTsf does not existError: [Scene] Remove Component failed: ebdiDBBPpIcYTyeWd4HTsf does not exist
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at NodeManager.removeComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:13514)
    at NodeManager.removeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:11257)
    at GeneralSceneFacade.removeNodeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:7692)
    at SceneFacadeManager.removeNodeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:11341)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:10:36 - warn: [Scene] Remove Component failed: ebdiDBBPpIcYTyeWd4HTsf does not existError: [Scene] Remove Component failed: ebdiDBBPpIcYTyeWd4HTsf does not exist
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at NodeManager.removeComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:13514)
    at NodeManager.removeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:11257)
    at GeneralSceneFacade.removeNodeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:7692)
    at SceneFacadeManager.removeNodeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:11341)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:11:02 - error: [Scene] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.onLoad (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95168:16)
    at callOnLoadInTryCatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55211:125)
    at NodeActivator.activateComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55256:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58319:46)
    at NodeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:12439)
    at GeneralSceneFacade.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:11455)
    at SceneFacadeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:14033)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:11:02 - error: [Scene] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.onEnable (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95176:16)
    at eval (eval at tryCatchFunctor_EDITOR (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js), <anonymous>:4:10)
    at ComponentScheduler.enableComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:53242:19)
    at NodeActivator.activateComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55272:46)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58319:46)
    at NodeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:12439)
    at GeneralSceneFacade.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:11455)
    at SceneFacadeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:14033)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:11:45 - error: [Scene] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.onLoad (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95168:16)
    at callOnLoadInTryCatch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55211:125)
    at NodeActivator.activateComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55256:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58319:46)
    at NodeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:12439)
    at GeneralSceneFacade.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:11455)
    at SceneFacadeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:14033)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:11:45 - error: [Scene] Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.Error: Can't add component 'cc.Graphics' to panel because it conflicts with the existing 'cc.Sprite' derived component.
    at Node._checkMultipleComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55346:19)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58277:18)
    at Mask._createGraphics (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95254:48)
    at Mask._changeRenderType (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95233:18)
    at Mask.onEnable (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95176:16)
    at eval (eval at tryCatchFunctor_EDITOR (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js), <anonymous>:4:10)
    at ComponentScheduler.enableComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:53242:19)
    at NodeActivator.activateComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55272:46)
    at Node.addComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58319:46)
    at NodeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:12439)
    at GeneralSceneFacade.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:11455)
    at SceneFacadeManager.createComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:14033)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-16-2025 21:13:20 - error: [Scene] Cannot read properties of undefined (reading 'blendState')TypeError: Cannot read properties of undefined (reading 'blendState')
    at Graphics._updateBlendFunc (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:98654:59)
    at Graphics.updateMaterial (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:98577:16)
    at Mask._disableRender (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95303:26)
    at Mask.onDisable (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:95187:16)
    at eval (eval at tryCatchFunctor_EDITOR (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js), <anonymous>:4:10)
    at ComponentScheduler.disableComp (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:53259:17)
    at Mask.destroy (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:53627:48)
    at Node.removeComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:58376:31)
    at CompManager.removeComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/component/index.ccc:1:1387)
    at NodeManager.removeComponent (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:13486)
    at NodeManager.removeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/node/index.ccc:1:11257)
    at GeneralSceneFacade.removeNodeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/general-scene-facade.ccc:1:7692)
    at SceneFacadeManager.removeNodeArrayElement (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/facade/scene-facade-manager.ccc:1:11341)
    at step (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:1:732)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:401
    at new Promise (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/ipc.ccc:2:338
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-17-2025 22:12:27 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-17-2025 22:12:27 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-17-2025 22:12:27 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-17-2025 22:12:27 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-17-2025 22:12:27 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-17-2025 22:12:28 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-17-2025 22:12:28 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-17-2025 22:12:28 - log: [PreviewInEditor] 检测到普通游戏环境
7-17-2025 22:12:28 - log: [PreviewInEditor] 玩家数据保存成功
7-17-2025 22:12:28 - log: [PreviewInEditor] 玩家数据已重置
7-17-2025 22:12:28 - log: [PreviewInEditor] 玩家数据加载成功
7-17-2025 22:12:29 - log: [PreviewInEditor] 更新关卡显示
7-17-2025 22:12:29 - log: [PreviewInEditor] 关卡 level-1: 解锁状态 = true
7-17-2025 22:12:29 - log: [PreviewInEditor] 关卡 level-2: 解锁状态 = false
7-17-2025 22:12:29 - log: [PreviewInEditor] 关卡 level-3: 解锁状态 = false
7-17-2025 22:12:31 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-17-2025 22:12:31 - info: [PreviewInEditor] 预览环境初始化完毕
7-17-2025 22:12:45 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-17-2025 22:12:45 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-17-2025 22:12:45 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-17-2025 22:12:45 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-17-2025 22:12:45 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-17-2025 22:12:45 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-17-2025 22:12:45 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-17-2025 22:12:45 - log: [PreviewInEditor] 检测到普通游戏环境
7-17-2025 22:12:45 - log: [PreviewInEditor] 玩家数据保存成功
7-17-2025 22:12:45 - log: [PreviewInEditor] 玩家数据已重置
7-17-2025 22:12:45 - log: [PreviewInEditor] 玩家数据加载成功
7-17-2025 22:12:54 - log: [PreviewInEditor] 更新关卡显示
7-17-2025 22:12:54 - log: [PreviewInEditor] 关卡 level-1: 解锁状态 = true
7-17-2025 22:12:54 - log: [PreviewInEditor] 关卡 level-2: 解锁状态 = false
7-17-2025 22:12:54 - log: [PreviewInEditor] 关卡 level-3: 解锁状态 = false
7-17-2025 22:12:56 - log: [PreviewInEditor] level-1 car-1
7-17-2025 22:12:56 - log: [PreviewInEditor] 调用场景内容加载
7-17-2025 22:12:56 - log: [PreviewInEditor] 场景预制体加载完成，通过GameManager获取AI车辆列表...
7-17-2025 22:12:56 - log: [PreviewInEditor] AI车辆查找完成，找到 4 个AI车辆
7-17-2025 22:12:56 - log: [PreviewInEditor] AIPlayer BoxCollider2D component found
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条已分离到Canvas下
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条UI初始化完成
7-17-2025 22:12:56 - log: [PreviewInEditor] AIPlayer BoxCollider2D component found
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条已分离到Canvas下
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条UI初始化完成
7-17-2025 22:12:56 - log: [PreviewInEditor] AIPlayer BoxCollider2D component found
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条已分离到Canvas下
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条UI初始化完成
7-17-2025 22:12:56 - log: [PreviewInEditor] AIPlayer BoxCollider2D component found
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条已分离到Canvas下
7-17-2025 22:12:56 - log: [PreviewInEditor] 血条UI初始化完成
7-17-2025 22:12:56 - log: [PreviewInEditor] 玩家血量初始化完成: 200/200
7-17-2025 22:12:56 - log: [PreviewInEditor] 生成车辆在右侧
7-17-2025 22:12:56 - log: [PreviewInEditor] BoxCollider2D component found and registered
7-17-2025 22:12:59 - log: [PreviewInEditor] AIPlayer collided with something
7-17-2025 22:12:59 - log: [PreviewInEditor] AIPlayer collided with something
7-17-2025 22:12:59 - log: [PreviewInEditor] AIPlayer collided with something
7-17-2025 22:12:59 - log: [PreviewInEditor] AIPlayer collided with something
7-17-2025 22:12:59 - log: [PreviewInEditor] AIPlayer collided with something
7-17-2025 22:13:00 - log: [PreviewInEditor] AIPlayer collided with something
7-17-2025 22:13:01 - log: [PreviewInEditor] AIPlayer collided with something
7-17-2025 22:13:03 - log: [PreviewInEditor] 玩家车辆发生碰撞，碰撞对象: level-1
7-17-2025 22:13:03 - log: [PreviewInEditor] 玩家车辆与地图边界碰撞，速度: 12.154282808388153, 伤害: 4
7-17-2025 22:13:03 - log: [PreviewInEditor] 玩家受到伤害: 4, 剩余生命值: 196
7-17-2025 22:13:04 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-17-2025 22:13:04 - info: [PreviewInEditor] 预览环境初始化完毕
7-17-2025 22:13:10 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-17-2025 22:13:10 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-17-2025 22:13:10 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-17-2025 22:13:10 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-17-2025 22:13:10 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-17-2025 22:13:10 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-17-2025 22:13:10 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-17-2025 22:13:11 - warn: [PreviewInEditor] The node can not be made persist because it's not under root node.Error: [PreviewInEditor] The node can not be made persist because it's not under root node.
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178301:12)
    at warnID (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178489:5)
    at Director.addPersistRootNode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:8102:17)
    at SceneFader.onLoad (file:///Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts:22:18)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55000:132)
    at OneOffInvoker.eval [as _invoke] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:52814:9)
    at OneOffInvoker.invoke (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:52928:16)
    at NodeActivator.activateNode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55051:27)
    at Scene._activate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:62871:44)
    at Director.runSceneImmediate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:7715:17)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scene/utils.ccc:1:1367
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:105199:17)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112943:9)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:194690:9)
    at sentryWrapped (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/src/helpers.ts:100:17)
7-17-2025 22:13:11 - log: [PreviewInEditor] 检测到普通游戏环境
7-17-2025 22:13:11 - log: [PreviewInEditor] 玩家数据保存成功
7-17-2025 22:13:11 - log: [PreviewInEditor] 玩家数据已重置
7-17-2025 22:13:11 - log: [PreviewInEditor] 玩家数据加载成功
7-17-2025 22:13:13 - log: [PreviewInEditor] 更新关卡显示
7-17-2025 22:13:13 - log: [PreviewInEditor] 关卡 level-1: 解锁状态 = true
7-17-2025 22:13:13 - log: [PreviewInEditor] 关卡 level-2: 解锁状态 = false
7-17-2025 22:13:13 - log: [PreviewInEditor] 关卡 level-3: 解锁状态 = false
7-17-2025 22:13:13 - error: [PreviewInEditor] Cannot set properties of null (setting 'opacity')TypeError: Cannot set properties of null (setting 'opacity')
    at SceneFader.fadeIn (file:///Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts:40:9)
    at file:///Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts:57:22
    at Director.runSceneImmediate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:7726:13)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:7781:22)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:107247:15)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112943:9)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:194690:9)
    at sentryWrapped (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/src/helpers.ts:100:17)
7-17-2025 22:13:15 - info: [PreviewInEditor] 预览环境初始化完毕
7-17-2025 22:15:42 - log: [PreviewInEditor] meshopt wasm decoder initialized
7-17-2025 22:15:42 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
7-17-2025 22:15:42 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
7-17-2025 22:15:42 - log: [PreviewInEditor] [PHYSICS]: using builtin.
7-17-2025 22:15:42 - log: [PreviewInEditor] Cocos Creator v3.8.6
7-17-2025 22:15:42 - log: [PreviewInEditor] Using custom pipeline: Builtin
7-17-2025 22:15:42 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to box2d.
7-17-2025 22:15:43 - warn: [PreviewInEditor] The node can not be made persist because it's not under root node.Error: [PreviewInEditor] The node can not be made persist because it's not under root node.
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178301:12)
    at warnID (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:178489:5)
    at Director.addPersistRootNode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:8102:17)
    at SceneFader.onLoad (file:///Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts:22:18)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55000:132)
    at OneOffInvoker.eval [as _invoke] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:52814:9)
    at OneOffInvoker.invoke (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:52928:16)
    at NodeActivator.activateNode (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:55051:27)
    at Scene._activate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:62871:44)
    at Director.runSceneImmediate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:7715:17)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scene/utils.ccc:1:1367
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:105199:17)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112943:9)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:194690:9)
    at sentryWrapped (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/src/helpers.ts:100:17)
7-17-2025 22:15:43 - log: [PreviewInEditor] 检测到普通游戏环境
7-17-2025 22:15:43 - log: [PreviewInEditor] 玩家数据保存成功
7-17-2025 22:15:43 - log: [PreviewInEditor] 玩家数据已重置
7-17-2025 22:15:43 - log: [PreviewInEditor] 玩家数据加载成功
7-17-2025 22:15:46 - log: [PreviewInEditor] 更新关卡显示
7-17-2025 22:15:46 - log: [PreviewInEditor] 关卡 level-1: 解锁状态 = true
7-17-2025 22:15:46 - log: [PreviewInEditor] 关卡 level-2: 解锁状态 = false
7-17-2025 22:15:46 - log: [PreviewInEditor] 关卡 level-3: 解锁状态 = false
7-17-2025 22:15:46 - error: [PreviewInEditor] Cannot set properties of null (setting 'opacity')TypeError: Cannot set properties of null (setting 'opacity')
    at SceneFader.fadeIn (file:///Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts:42:9)
    at file:///Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts:59:22
    at Director.runSceneImmediate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:7726:13)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:7781:22)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:107247:15)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112943:9)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:194690:9)
    at sentryWrapped (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/src/helpers.ts:100:17)
7-17-2025 22:16:07 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at IpcRenderer.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/ipc/web/webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
7-17-2025 22:16:07 - info: [PreviewInEditor] 预览环境初始化完毕
7-17-2025 22:16:45 - warn: [Scene] Missing class: b1516dN7c5JWafFHZhyMU6TError: [Scene] Missing class: b1516dN7c5JWafFHZhyMU6T
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at Object.classFinder (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/engine/dist/editor-extends/missing-reporter/missing-class-reporter.ccc:7:982)
    at _Deserializer.classFinder [as _classFinder] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:107827:34)
    at _Deserializer._deserializeTypeTaggedObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49466:30)
    at _Deserializer._deserializeObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49420:29)
    at _Deserializer._deserializeAndAssignField (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49587:38)
    at _Deserializer._deserializeArray (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49448:40)
    at _Deserializer._deserializeObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49426:29)
    at _Deserializer._deserializeAndAssignField (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49599:36)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49188:15)
    at _Deserializer._deserializeFireClass (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49570:11)
    at _Deserializer._deserializeInto (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49512:18)
    at _Deserializer._deserializeTypeTaggedObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49484:18)
    at _Deserializer._deserializeObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49420:29)
    at _Deserializer._deserializeAndAssignField (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49587:38)
    at _Deserializer._deserializeArray (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49448:40)
    at _Deserializer._deserializeObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49426:29)
    at _Deserializer._deserializeAndAssignField (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49599:36)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49188:15)
    at _Deserializer._deserializeFireClass (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49570:11)
    at _Deserializer._deserializeInto (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49512:18)
    at _Deserializer._deserializeTypeTaggedObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49484:18)
    at _Deserializer._deserializeObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49420:29)
    at _Deserializer._deserializeAndAssignField (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49587:38)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49188:15)
    at _Deserializer._deserializeFireClass (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49570:11)
    at _Deserializer._deserializeInto (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49512:18)
    at _Deserializer._deserializeTypeTaggedObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49484:18)
    at _Deserializer._deserializeObject (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49420:29)
    at _Deserializer.deserialize (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49392:42)
    at deserializeDynamic (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:49242:30)
    at deserialize (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:47282:13)
    at deserializeAsset (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:107840:15)
    at parseImport (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:110892:22)
    at Parser.parse (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:110975:11)
    at parse (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112389:20)
    at Pipeline._flow (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:111258:11)
    at eval (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:111268:22)
    at fetch (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112324:11)
    at Pipeline._flow (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:111258:11)
    at Pipeline.async (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:111254:16)
    at options.__exclude__ (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112168:28)
    at forEach (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112854:7)
    at load (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:112145:5)
    at Pipeline._flow (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:111258:11)
    at Pipeline.async (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:111254:16)
    at AssetManager.loadWithJson (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/bin/.cache/dev/editor/bundled/index.js:105203:31)
7-17-2025 22:16:45 - error: [Scene] Script "b1516dN7c5JWafFHZhyMU6T" attached to "fader" is missing or invalid. Detailed information:
Node path: "fader"
Script file: "/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts"
Script deleted time: "2025/7/17 22:16:45"
Script UUID: "b151674d-edce-4959-a7c5-1d9872314e93"
Class ID: "b1516dN7c5JWafFHZhyMU6T"
Error: [Scene] Script "b1516dN7c5JWafFHZhyMU6T" attached to "fader" is missing or invalid. Detailed information:
Node path: "fader"
Script file: "/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts"
Script deleted time: "2025/7/17 22:16:45"
Script UUID: "b151674d-edce-4959-a7c5-1d9872314e93"
Class ID: "b1516dN7c5JWafFHZhyMU6T"

    at console.error (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at report (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/engine/dist/editor-extends/missing-reporter/missing-class-reporter.ccc:7:25)
    at reportByWalker (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/engine/dist/editor-extends/missing-reporter/missing-class-reporter.ccc:7:127)
