19:42:19.380 debug: 2025/7/16 19:42:19
19:42:19.380 debug: Project: /Users/<USER>/projects/cocos_project/driftClash
19:42:19.380 debug: Targets: editor,preview
19:42:19.381 debug: Incremental file seems great.
19:42:19.381 debug: Engine path: /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine
19:42:19.385 debug: Initializing target [Editor]
19:42:19.386 debug: Loading cache
19:42:19.386 debug: Loading cache costs 0.8392499999999927ms.
19:42:19.387 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
19:42:19.387 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
19:42:19.387 debug: Initializing target [Preview]
19:42:19.387 debug: Loading cache
19:42:19.387 debug: Loading cache costs 0.6067920000000413ms.
19:42:19.387 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
19:42:19.406 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,mask,particle-2d,physics-2d-box2d,profiler,rich-text,spine-3.8,tiled-map,tween,ui,video,webview,custom-pipeline
19:42:19.407 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets"
  },
  {
    "root": "db://assets/",
    "physical": "/Users/<USER>/projects/cocos_project/driftClash/assets"
  }
]
19:42:19.407 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/"
  }
}
19:42:19.408 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/"
  }
}
19:42:19.408 debug: Pulling asset-db.
19:42:19.411 debug: Fetch asset-db cost: 2.8584999999998217ms.
19:42:19.411 debug: Build iteration starts.
Number of accumulated asset changes: 24
Feature changed: false
19:42:19.411 debug: Target(editor) build started.
19:42:19.412 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
19:42:19.412 debug: Inspect cce:/internal/x/cc
19:42:19.430 debug: transform url: 'cce:/internal/x/cc' costs: 18.00 ms
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
19:42:19.431 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
19:42:19.431 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
19:42:19.431 debug: Inspect cce:/internal/x/prerequisite-imports
19:42:19.437 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 5.60 ms
19:42:19.438 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
19:42:19.438 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:42:19.438 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.438 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.438 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:42:19.438 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.438 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
19:42:19.439 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
19:42:19.439 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.439 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.439 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Tue Jul 15 2025 00:47:22 GMT+0800 (中国标准时间), Current mtime: Wed Jul 16 2025 19:42:16 GMT+0800 (中国标准时间)
19:42:19.439 debug: Inspect cce:/internal/code-quality/cr.mjs
19:42:19.442 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 3.10 ms
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
19:42:19.442 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.442 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:42:19.442 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:42:19.442 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
19:42:19.442 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:42:19.442 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.443 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
19:42:19.443 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
19:42:19.443 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.443 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.443 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
19:42:19.443 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.443 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
19:42:19.443 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.443 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.443 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.443 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
19:42:19.443 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.444 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.444 debug: Resolve ./SelectManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.444 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.444 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.444 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.444 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.444 debug: Resolve ./SelectManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
19:42:19.444 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.444 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.445 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.445 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/x/cc.
19:42:19.445 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.445 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.448 debug: Target(editor) ends with cost 37.204291999999896ms.
19:42:19.448 debug: Target(preview) build started.
19:42:19.449 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
19:42:19.449 debug: Inspect cce:/internal/x/cc
19:42:19.458 debug: transform url: 'cce:/internal/x/cc' costs: 8.60 ms
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
19:42:19.458 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
19:42:19.458 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
19:42:19.458 debug: Inspect cce:/internal/x/prerequisite-imports
19:42:19.462 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.90 ms
19:42:19.463 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
19:42:19.463 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:42:19.463 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.463 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.463 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:42:19.463 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.463 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.464 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
19:42:19.464 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
19:42:19.464 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.464 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.464 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Tue Jul 15 2025 00:47:22 GMT+0800 (中国标准时间), Current mtime: Wed Jul 16 2025 19:42:16 GMT+0800 (中国标准时间)
19:42:19.464 debug: Inspect cce:/internal/code-quality/cr.mjs
19:42:19.468 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 4.50 ms
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
19:42:19.469 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.469 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:42:19.469 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:42:19.469 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
19:42:19.469 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.469 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
19:42:19.469 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
19:42:19.469 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.469 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.469 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.469 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
19:42:19.469 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.470 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
19:42:19.470 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.470 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
19:42:19.470 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.470 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.470 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.470 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.470 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.470 debug: Resolve ./SelectManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.470 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.470 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.470 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.470 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
19:42:19.470 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.471 debug: Resolve ./SelectManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.471 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.471 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.471 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/code-quality/cr.mjs.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as cce:/internal/x/cc.
19:42:19.471 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.471 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.474 debug: Target(preview) ends with cost 25.7217499999997ms.
19:42:19.728 debug: Pulling asset-db.
19:42:19.962 debug: Fetch asset-db cost: 234.3330420000002ms.
19:42:19.965 debug: Build iteration starts.
Number of accumulated asset changes: 24
Feature changed: false
19:42:19.965 debug: Target(editor) build started.
19:42:19.974 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
19:42:19.974 debug: Inspect cce:/internal/x/prerequisite-imports
19:42:19.981 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 6.90 ms
19:42:19.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
19:42:19.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:42:19.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:42:19.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
19:42:19.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:19.985 debug: Target(editor) ends with cost 20.072375000000193ms.
19:42:19.986 debug: Target(preview) build started.
19:42:19.986 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
19:42:19.986 debug: Inspect cce:/internal/x/prerequisite-imports
19:42:19.990 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.30 ms
19:42:19.990 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
19:42:19.990 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:42:19.990 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:42:19.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:42:19.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:42:19.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
19:42:19.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
19:42:19.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
19:42:19.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
19:42:19.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
19:42:19.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
19:42:19.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
19:42:20.012 debug: Target(preview) ends with cost 26.65545800000018ms.
22:11:30.337 debug: Dispatch build request for time accumulated 3 asset changes.
22:11:30.340 debug: Build iteration starts.
Number of accumulated asset changes: 3
Feature changed: false
22:11:30.340 debug: Target(editor) build started.
22:11:30.342 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Fri Jan 02 1970 10:29:13 GMT+0800 (中国标准时间)
22:11:30.342 debug: Inspect cce:/internal/x/prerequisite-imports
22:11:30.357 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 14.90 ms
22:11:30.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
22:11:30.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
22:11:30.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
22:11:30.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
22:11:30.359 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
22:11:30.359 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
22:11:30.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
22:11:30.359 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts. Last mtime: Wed Jul 02 2025 21:13:21 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f, Current mtime: Thu Jul 17 2025 22:07:39 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f
22:11:30.359 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
22:11:30.385 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts' costs: 26.20 ms
22:11:30.386 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
22:11:30.386 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
22:11:30.386 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:11:30.386 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
22:11:30.386 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:11:30.386 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
22:11:30.398 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts' costs: 11.10 ms
22:11:30.398 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
22:11:30.398 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts. Last mtime: Sat Jul 12 2025 12:23:26 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f, Current mtime: Thu Jul 17 2025 22:11:25 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f
22:11:30.398 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
22:11:30.421 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts' costs: 22.90 ms
22:11:30.422 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:11:30.422 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
22:11:30.422 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
22:11:30.422 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
22:11:30.422 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
22:11:30.422 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:11:30.422 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:11:30.422 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:11:30.422 debug: Resolve ./SceneFader from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:11:30.422 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:11:30.422 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:11:30.422 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:11:30.423 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:11:30.423 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:11:30.423 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
22:11:30.423 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:11:30.423 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:11:30.423 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:11:30.423 debug: Resolve ./SceneFader from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:11:30.425 debug: Target(editor) ends with cost 84.34937499463558ms.
22:11:30.425 debug: Target(preview) build started.
22:11:30.425 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Fri Jan 02 1970 10:29:13 GMT+0800 (中国标准时间)
22:11:30.425 debug: Inspect cce:/internal/x/prerequisite-imports
22:11:30.429 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 4.20 ms
22:11:30.430 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
22:11:30.430 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
22:11:30.430 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
22:11:30.430 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
22:11:30.430 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
22:11:30.430 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
22:11:30.430 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
22:11:30.430 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
22:11:30.430 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
22:11:30.430 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
22:11:30.430 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
22:11:30.431 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
22:11:30.431 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
22:11:30.431 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
22:11:30.431 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
22:11:30.431 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts. Last mtime: Wed Jul 02 2025 21:13:21 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f, Current mtime: Thu Jul 17 2025 22:07:39 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f
22:11:30.431 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
22:11:30.446 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts' costs: 15.10 ms
22:11:30.446 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
22:11:30.446 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
22:11:30.446 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:11:30.446 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
22:11:30.446 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:11:30.446 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
22:11:30.462 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts' costs: 15.50 ms
22:11:30.462 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
22:11:30.462 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts. Last mtime: Sat Jul 12 2025 12:23:26 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f, Current mtime: Thu Jul 17 2025 22:11:25 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f
22:11:30.463 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
22:11:30.491 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts' costs: 28.50 ms
22:11:30.492 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:11:30.492 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
22:11:30.492 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
22:11:30.492 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
22:11:30.492 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
22:11:30.492 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve ./SceneFader from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:11:30.493 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:11:30.493 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:11:30.493 debug: Resolve ./SceneFader from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:11:30.499 debug: Target(preview) ends with cost 74.44691599905491ms.
22:15:41.360 debug: Dispatch build request for time accumulated 1 asset changes.
22:15:41.364 debug: Build iteration starts.
Number of accumulated asset changes: 1
Feature changed: false
22:15:41.364 debug: Target(editor) build started.
22:15:41.366 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Fri Jan 02 1970 10:29:13 GMT+0800 (中国标准时间), Current mtime: Fri Jan 02 1970 10:33:24 GMT+0800 (中国标准时间)
22:15:41.366 debug: Inspect cce:/internal/x/prerequisite-imports
22:15:41.376 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 10.60 ms
22:15:41.377 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
22:15:41.378 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
22:15:41.378 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
22:15:41.378 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
22:15:41.378 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
22:15:41.378 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
22:15:41.378 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
22:15:41.378 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
22:15:41.378 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
22:15:41.378 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
22:15:41.379 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
22:15:41.379 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
22:15:41.379 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
22:15:41.379 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
22:15:41.379 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
22:15:41.380 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
22:15:41.380 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
22:15:41.380 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:15:41.380 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
22:15:41.380 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:15:41.380 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts. Last mtime: Thu Jul 17 2025 22:10:10 GMT+0800 (中国标准时间)@b151674d-edce-4959-a7c5-1d9872314e93, Current mtime: Thu Jul 17 2025 22:14:35 GMT+0800 (中国标准时间)@b151674d-edce-4959-a7c5-1d9872314e93
22:15:41.380 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
22:15:41.391 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts' costs: 11.40 ms
22:15:41.392 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
22:15:41.392 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:15:41.392 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
22:15:41.392 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
22:15:41.392 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
22:15:41.393 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:15:41.393 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:15:41.393 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:15:41.400 debug: Target(editor) ends with cost 35.38033400475979ms.
22:15:41.400 debug: Target(preview) build started.
22:15:41.401 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Fri Jan 02 1970 10:29:13 GMT+0800 (中国标准时间), Current mtime: Fri Jan 02 1970 10:33:24 GMT+0800 (中国标准时间)
22:15:41.401 debug: Inspect cce:/internal/x/prerequisite-imports
22:15:41.413 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 12.20 ms
22:15:41.414 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
22:15:41.414 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
22:15:41.414 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
22:15:41.414 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
22:15:41.414 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
22:15:41.414 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
22:15:41.415 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts.
22:15:41.415 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts. Last mtime: Thu Jul 17 2025 22:10:10 GMT+0800 (中国标准时间)@b151674d-edce-4959-a7c5-1d9872314e93, Current mtime: Thu Jul 17 2025 22:14:35 GMT+0800 (中国标准时间)@b151674d-edce-4959-a7c5-1d9872314e93
22:15:41.415 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts
22:15:41.435 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts' costs: 19.90 ms
22:15:41.435 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
22:15:41.435 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:15:41.435 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
22:15:41.435 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
22:15:41.435 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
22:15:41.436 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:15:41.436 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:15:41.436 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts as cce:/internal/x/cc.
22:15:41.441 debug: Target(preview) ends with cost 40.41166599094868ms.
22:16:45.232 debug: Dispatch build request for time accumulated 3 asset changes.
22:16:45.234 debug: Build iteration starts.
Number of accumulated asset changes: 3
Feature changed: false
22:16:45.235 debug: Target(editor) build started.
22:16:45.237 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Fri Jan 02 1970 10:33:24 GMT+0800 (中国标准时间), Current mtime: Fri Jan 02 1970 10:34:28 GMT+0800 (中国标准时间)
22:16:45.237 debug: Inspect cce:/internal/x/prerequisite-imports
22:16:45.279 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 41.90 ms
22:16:45.280 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
22:16:45.280 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
22:16:45.280 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
22:16:45.280 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
22:16:45.280 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
22:16:45.280 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
22:16:45.280 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
22:16:45.280 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
22:16:45.280 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
22:16:45.280 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
22:16:45.281 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
22:16:45.281 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
22:16:45.281 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
22:16:45.281 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
22:16:45.281 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
22:16:45.281 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts. Last mtime: Thu Jul 17 2025 22:07:39 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f, Current mtime: Thu Jul 17 2025 22:16:38 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f
22:16:45.281 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
22:16:45.298 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts' costs: 17.50 ms
22:16:45.299 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
22:16:45.299 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
22:16:45.299 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:16:45.299 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
22:16:45.299 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
22:16:45.299 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts. Last mtime: Thu Jul 17 2025 22:11:25 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f, Current mtime: Thu Jul 17 2025 22:16:40 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f
22:16:45.299 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
22:16:45.320 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts' costs: 20.30 ms
22:16:45.320 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:16:45.321 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
22:16:45.321 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
22:16:45.321 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
22:16:45.321 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
22:16:45.321 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:16:45.321 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:16:45.321 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:16:45.321 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:16:45.321 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:16:45.321 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:16:45.321 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:16:45.321 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:16:45.323 debug: Target(editor) ends with cost 88.7437499910593ms.
22:16:45.323 debug: Target(preview) build started.
22:16:45.324 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Fri Jan 02 1970 10:33:24 GMT+0800 (中国标准时间), Current mtime: Fri Jan 02 1970 10:34:28 GMT+0800 (中国标准时间)
22:16:45.324 debug: Inspect cce:/internal/x/prerequisite-imports
22:16:45.327 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.40 ms
22:16:45.328 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
22:16:45.328 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
22:16:45.328 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
22:16:45.328 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
22:16:45.328 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
22:16:45.328 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts.
22:16:45.328 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
22:16:45.328 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts. Last mtime: Thu Jul 17 2025 22:07:39 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f, Current mtime: Thu Jul 17 2025 22:16:38 GMT+0800 (中国标准时间)@0cf6405c-918a-40f1-b383-68a9fc739b7f
22:16:45.328 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts
22:16:45.338 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts' costs: 9.40 ms
22:16:45.338 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
22:16:45.338 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
22:16:45.338 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:16:45.338 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts.
22:16:45.338 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
22:16:45.338 debug: Detected change: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts. Last mtime: Thu Jul 17 2025 22:11:25 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f, Current mtime: Thu Jul 17 2025 22:16:40 GMT+0800 (中国标准时间)@be7b2dc0-da35-4de9-a81c-30690fdcd28f
22:16:45.338 debug: Inspect file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts
22:16:45.361 debug: transform url: 'file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts' costs: 23.20 ms
22:16:45.362 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:16:45.363 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts.
22:16:45.363 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
22:16:45.363 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
22:16:45.364 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
22:16:45.364 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:16:45.364 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:16:45.364 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
22:16:45.364 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
22:16:45.364 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
22:16:45.364 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:16:45.364 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:16:45.364 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
22:16:45.368 debug: Target(preview) ends with cost 44.890249997377396ms.
