{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4K,uCAA5K,EAA4V,uCAA5V,EAAghB,uCAAhhB,EAAisB,uCAAjsB,EAA42B,uCAA52B,EAAghC,uCAAhhC,EAAwnC,uCAAxnC,EAA4tC,uCAA5tC,EAAu0C,uCAAv0C,EAA86C,wCAA96C,EAA4hD,wCAA5hD,EAAqoD,wCAAroD,EAA4uD,wCAA5uD,EAAs1D,wCAAt1D,EAAo8D,wCAAp8D,EAA0iE,wCAA1iE,EAAkpE,wCAAlpE,EAA2vE,wCAA3vE,EAA22E,wCAA32E,EAAi9E,wCAAj9E,EAA2jF,wCAA3jF,EAAoqF,wCAApqF,EAAwwF,wCAAxwF,EAA03F,wCAA13F,EAAm+F,wCAAn+F,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFaderTest.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}