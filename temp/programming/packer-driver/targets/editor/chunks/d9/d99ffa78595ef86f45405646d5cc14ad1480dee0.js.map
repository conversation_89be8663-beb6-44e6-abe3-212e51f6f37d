{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"], "names": ["_decorator", "Component", "Node", "Prefab", "instantiate", "resources", "UITransform", "director", "ProgressBar", "Label", "<PERSON><PERSON>", "TempData", "CameraFollow", "player", "AIController", "AIPlayer", "<PERSON><PERSON><PERSON><PERSON>", "SceneFader", "ccclass", "property", "GameState", "GameManager", "aiPlayers", "currentState", "RUNNING", "gameStartTime", "gameEndTime", "playerHP", "playerMaxHP", "enemyCount", "initialEnemyCount", "playerComponent", "getInstance", "_instance", "onLoad", "console", "log", "node", "destroy", "start", "initializeGame", "bindButtonEvents", "loadLevelAndCar", "onDestroy", "Date", "now", "pausePanel", "active", "gameOverPanel", "pauseButton", "on", "EventType", "CLICK", "pauseGame", "resumeButton", "resumeGame", "mainMenuButton2", "returnToMainMenu", "mainMenuButton", "levelId", "selectedLevel", "carId", "selectedCar", "mapNode", "player<PERSON>ode", "load", "err", "prefab", "setPosition", "playGround", "<PERSON><PERSON><PERSON><PERSON>", "autoFindAIPlayers", "notifyAIControllers", "length", "refreshEnemyCount", "err2", "prefab2", "spawnChildren", "spawnPoint", "children", "randomIndex", "Math", "floor", "random", "spawnNode", "spawnPos", "getWorldPosition", "localPos", "canvas", "getComponent", "convertToNodeSpaceAR", "setRotation", "getRotation", "playerScript", "init", "angle", "initializePlayerHealth", "indexOf", "name", "cameraFollow", "camera", "error", "scene", "getChildByName", "sceneNode", "carsNode", "carNode", "aiPlayer", "push", "getAIPlayers", "aiControllers", "getComponentsInChildren", "aiController", "onScenePrefabLoaded", "getMaxHealth", "refreshPlayerHealthBar", "reducePlayerHP", "amount", "max", "gameOver", "syncPlayerHealth", "getCurrentHealth", "resetPlayerHealth", "restoreVehicle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progress", "count", "enemyCountLabel", "string", "PAUSED", "pause", "resume", "isVictory", "GAME_OVER", "updateGameOverUI", "calculateAndGiveReward", "restartGame", "loadScene", "getScene", "gameOverTitleLabel", "gameTimeMs", "gameTimeSec", "updateRewardUI", "instance", "addMoney", "updateLevelProgress", "healthPercentage", "stars", "calculateStars", "performance", "reward", "calculatePerformance", "toFixed", "gameTime", "min", "currentLevelId", "warn", "score", "performance<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "getCurrentState", "getGameTime", "getPlayerHP", "getPlayerMaxHP", "getEnemyCount"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AACxGC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U,GAE9B;;2BACYoB,S,0BAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;eAAAA,S;;;6BAOCC,W,WADZH,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACjB,IAAD,C,UAGRiB,QAAQ,CAACjB,IAAD,C,UAGRiB,QAAQ,CAACjB,IAAD,C,UAGRiB,QAAQ,CAACjB,IAAD,C,UAGRiB,QAAQ,CAACX,WAAD,C,UAGRW,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACjB,IAAD,C,WAGRiB,QAAQ,CAACjB,IAAD,C,WAGRiB,QAAQ,CAACT,MAAD,C,WAGRS,QAAQ,CAACT,MAAD,C,WAGRS,QAAQ,CAACT,MAAD,C,WAGRS,QAAQ,CAACV,KAAD,C,WAGRU,QAAQ,CAACV,KAAD,C,WAGRU,QAAQ,CAACV,KAAD,C,sCA5Cb,MACaY,WADb,SACiCpB,SADjC,CAC2C;AAAA;AAAA;;AAAA;;AAEb;AAFa;;AAKjB;AALiB;;AAQb;AARa;;AAWjB;AAXiB;;AAcD;AAdC;;AAiBP;AAjBO;;AAoBV;AApBU;;AAuBb;AAvBa;;AA0BV;AA1BU;;AA6BT;AA7BS;;AAgCN;AAhCM;;AAmCP;AAnCO;;AAsCJ;AAtCI;;AAyCN;AAzCM;;AAAA,eA+C/BqB,SA/C+B,GA+CP,EA/CO;AAiDvC;AAjDuC,eAkD/BC,YAlD+B,GAkDLH,SAAS,CAACI,OAlDL;AAAA,eAmD/BC,aAnD+B,GAmDP,CAnDO;AAAA,eAoD/BC,WApD+B,GAoDT,CApDS;AAsDvC;AAtDuC,eAuD/BC,QAvD+B,GAuDZ,CAvDY;AAuDT;AAvDS,eAwD/BC,WAxD+B,GAwDT,CAxDS;AAwDN;AAxDM,eAyD/BC,UAzD+B,GAyDV,CAzDU;AAAA,eA0D/BC,iBA1D+B,GA0DH,CA1DG;AAAA,eA2D/BC,eA3D+B,GA2DE,IA3DF;AAAA;;AA2DQ;AAEtB,eAAXC,WAAW,GAAgB;AACrC,iBAAOX,WAAW,CAACY,SAAnB;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAIb,WAAW,CAACY,SAAhB,EAA2B;AACvBE,YAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AACA,iBAAKC,IAAL,CAAUC,OAAV;AACA;AACH;;AACDjB,UAAAA,WAAW,CAACY,SAAZ,GAAwB,IAAxB;AACH;;AAEDM,QAAAA,KAAK,GAAG;AACJJ,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA,eAAKI,cAAL;AACA,eAAKC,gBAAL;AACA,eAAKC,eAAL;AACH;;AACDC,QAAAA,SAAS,GAAG;AACP,cAAItB,WAAW,CAACY,SAAZ,KAA0B,IAA9B,EAAoC;AACjCZ,YAAAA,WAAW,CAACY,SAAZ,GAAwB,IAAxB;AACAE,YAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACH;AACR;AAEG;AACJ;AACA;;;AACYI,QAAAA,cAAc,GAAG;AACrB,eAAKjB,YAAL,GAAoBH,SAAS,CAACI,OAA9B;AACA,eAAKC,aAAL,GAAqBmB,IAAI,CAACC,GAAL,EAArB;AACA,eAAKnB,WAAL,GAAmB,CAAnB,CAHqB,CAKrB;;AACA,cAAI,KAAKoB,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACH;;AACD,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACYN,QAAAA,gBAAgB,GAAG;AACvB;AACA,cAAI,KAAKQ,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBZ,IAAjB,CAAsBa,EAAtB,CAAyBxC,MAAM,CAACyC,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,SAAtD,EAAiE,IAAjE;AACH,WAJsB,CAMvB;;;AACA,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBjB,IAAlB,CAAuBa,EAAvB,CAA0BxC,MAAM,CAACyC,SAAP,CAAiBC,KAA3C,EAAkD,KAAKG,UAAvD,EAAmE,IAAnE;AACH,WATsB,CAWvB;;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBnB,IAArB,CAA0Ba,EAA1B,CAA6BxC,MAAM,CAACyC,SAAP,CAAiBC,KAA9C,EAAqD,KAAKK,gBAA1D,EAA4E,IAA5E;AACH,WAdsB,CAgBvB;;;AACA,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBrB,IAApB,CAAyBa,EAAzB,CAA4BxC,MAAM,CAACyC,SAAP,CAAiBC,KAA7C,EAAoD,KAAKK,gBAAzD,EAA2E,IAA3E;AACH;AACJ;;AAEDf,QAAAA,eAAe,GAAG;AACd,gBAAMiB,OAAO,GAAG;AAAA;AAAA,oCAASC,aAAzB;AACA,gBAAMC,KAAK,GAAG;AAAA;AAAA,oCAASC,WAAvB;AACA,cAAIC,OAAoB,GAAG,IAA3B;AACA,cAAIC,UAAuB,GAAG,IAA9B,CAJc,CAKd;;AACA,cAAIL,OAAJ,EAAa;AACTtD,YAAAA,SAAS,CAAC4D,IAAV,CAAgB,iBAAgBN,OAAQ,EAAxC,EAA2CxD,MAA3C,EAAmD,CAAC+D,GAAD,EAAMC,MAAN,KAAiB;AAChE,kBAAI,CAACD,GAAD,IAAQC,MAAZ,EAAoB;AAChBJ,gBAAAA,OAAO,GAAG3D,WAAW,CAAC+D,MAAD,CAArB;AACAJ,gBAAAA,OAAO,CAACK,WAAR,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACA,qBAAKC,UAAL,CAAgBC,QAAhB,CAAyBP,OAAzB,EAHgB,CAKhB;;AACA,qBAAKQ,iBAAL;AACA,qBAAKC,mBAAL,GAPgB,CAShB;;AACA,qBAAK1C,iBAAL,GAAyB,KAAKR,SAAL,CAAemD,MAAxC;AACA,qBAAKC,iBAAL,CAAuB,KAAK5C,iBAA5B,EAXgB,CAahB;;AACA,oBAAI+B,KAAJ,EAAW;AACPxD,kBAAAA,SAAS,CAAC4D,IAAV,CAAgB,eAAcJ,KAAM,EAApC,EAAuC1D,MAAvC,EAA+C,CAACwE,IAAD,EAAOC,OAAP,KAAmB;AAC9D,wBAAI,CAACD,IAAD,IAASC,OAAb,EAAsB;AAClBZ,sBAAAA,UAAU,GAAG5D,WAAW,CAACwE,OAAD,CAAxB,CADkB,CAElB;;AACA,4BAAMC,aAAa,GAAG,KAAKC,UAAL,CAAgBC,QAAtC;;AACA,0BAAIF,aAAa,CAACJ,MAAd,GAAuB,CAA3B,EAA8B;AAC1B,8BAAMO,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBN,aAAa,CAACJ,MAAzC,CAApB;AACA,8BAAMW,SAAS,GAAGP,aAAa,CAACG,WAAD,CAA/B;AACA,8BAAMK,QAAQ,GAAGD,SAAS,CAACE,gBAAV,EAAjB,CAH0B,CAI1B;;AACA,8BAAMC,QAAQ,GAAG,KAAKC,MAAL,CAAYC,YAAZ,CAAyBnF,WAAzB,EAAsCoF,oBAAtC,CAA2DL,QAA3D,CAAjB;AACArB,wBAAAA,UAAU,CAACI,WAAX,CAAuBmB,QAAvB;AACAvB,wBAAAA,UAAU,CAAC2B,WAAX,CAAuBP,SAAS,CAACQ,WAAV,EAAvB,EAP0B,CAQ1B;;AACA,8BAAMC,YAAY,GAAG7B,UAAU,CAACyB,YAAX;AAAA;AAAA,6CAArB;;AACA,4BAAII,YAAJ,EAAkB;AACdA,0BAAAA,YAAY,CAACC,IAAb,CAAkBV,SAAS,CAACW,KAA5B,EADc,CAEd;;AACA,+BAAKC,sBAAL,CAA4BH,YAA5B;AACH,yBAdyB,CAe1B;;;AACA,4BAAI,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB,EAA+BI,OAA/B,CAAuCb,SAAS,CAACc,IAAjD,MAA2D,CAAC,CAAhE,EAAmE;AAC/D/D,0BAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAD+D,CAE/D;AACH;AACJ;;AACD,2BAAKoD,MAAL,CAAYlB,QAAZ,CAAqBN,UAArB,EAzBkB,CA0BlB;;AACA,4BAAMmC,YAAY,GAAG,KAAKC,MAAL,CAAYX,YAAZ;AAAA;AAAA,uDAArB;;AACA,0BAAIU,YAAY,IAAIpC,OAAhB,IAA2BC,UAA/B,EAA2C;AACvCmC,wBAAAA,YAAY,CAACL,IAAb,CAAkB/B,OAAlB,EAA2BC,UAA3B;AACH;AACJ;;AACD,wBAAIW,IAAJ,EAAU;AACNxC,sBAAAA,OAAO,CAACkE,KAAR,CAAc,YAAd,EAA4B1B,IAA5B,EAAkCd,KAAlC;AACA;AACH;;AACD,wBAAI,CAACe,OAAL,EAAc;AACVzC,sBAAAA,OAAO,CAACkE,KAAR,CAAc,WAAd,EAA2BxC,KAA3B;AACA;AACH;AACJ,mBAzCD;AA0CH;AACJ;;AACD,kBAAIK,GAAJ,EAAS;AACL/B,gBAAAA,OAAO,CAACkE,KAAR,CAAc,YAAd,EAA4BnC,GAA5B,EAAiCP,OAAjC;AACA;AACH;;AACD,kBAAI,CAACQ,MAAL,EAAa;AACThC,gBAAAA,OAAO,CAACkE,KAAR,CAAc,WAAd,EAA2B1C,OAA3B;AACA;AACH;AACJ,aApED;AAqEH;AACJ;AAED;AACJ;AACA;;;AACWY,QAAAA,iBAAiB,GAAG;AACvB,eAAKjD,SAAL,GAAiB,EAAjB,CADuB,CAEvB;;AACA,gBAAMgF,KAAK,GAAG,KAAKjE,IAAL,CAAUiE,KAAxB;AACA,cAAI,CAACA,KAAL,EAAY;AACZ,gBAAMd,MAAM,GAAGc,KAAK,CAACC,cAAN,CAAqB,QAArB,CAAf;AACA,cAAI,CAACf,MAAL,EAAa;AACb,gBAAMnB,UAAU,GAAGmB,MAAM,CAACe,cAAP,CAAsB,YAAtB,CAAnB;AACA,cAAI,CAAClC,UAAL,EAAiB;AACjB,gBAAMmC,SAAS,GAAGnC,UAAU,CAACU,QAAX,CAAoB,CAApB,CAAlB;AACA,cAAI,CAACyB,SAAL,EAAgB;AAChB,gBAAMC,QAAQ,GAAGD,SAAS,CAACD,cAAV,CAAyB,MAAzB,CAAjB;AACA,cAAI,CAACE,QAAL,EAAe;;AACf,eAAK,MAAMC,OAAX,IAAsBD,QAAQ,CAAC1B,QAA/B,EAAyC;AACrC,kBAAM4B,QAAQ,GAAGD,OAAO,CAACjB,YAAR;AAAA;AAAA,qCAAjB;;AACA,gBAAIkB,QAAJ,EAAc;AACV,mBAAKrF,SAAL,CAAesF,IAAf,CAAoBD,QAApB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWE,QAAAA,YAAY,GAAe;AAC9B,iBAAO,KAAKvF,SAAZ;AACH;AAED;AACJ;AACA;;;AACYkD,QAAAA,mBAAmB,GAAG;AAC1B,gBAAMsC,aAAa,GAAG,KAAKzE,IAAL,CAAUiE,KAAV,CAAgBS,uBAAhB;AAAA;AAAA,2CAAtB;;AACA,eAAK,MAAMC,YAAX,IAA2BF,aAA3B,EAA0C;AACtCE,YAAAA,YAAY,CAACC,mBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYjB,QAAAA,sBAAsB,CAACH,YAAD,EAAuB;AACjD,eAAK9D,eAAL,GAAuB8D,YAAvB;AACA,eAAKjE,WAAL,GAAmBiE,YAAY,CAACqB,YAAb,EAAnB;AACA,eAAKvF,QAAL,GAAgB,KAAKC,WAArB;AAEAO,UAAAA,OAAO,CAACC,GAAR,CAAa,cAAa,KAAKT,QAAS,IAAG,KAAKC,WAAY,EAA5D,EALiD,CAOjD;;AACA,eAAKuF,sBAAL;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,cAAc,CAACC,MAAD,EAAiB;AAClClF,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuBiF,MAAvB;AACA,eAAK1F,QAAL,GAAgBsD,IAAI,CAACqC,GAAL,CAAS,CAAT,EAAY,KAAK3F,QAAL,GAAgB0F,MAA5B,CAAhB;AACA,eAAKF,sBAAL,GAHkC,CAKlC;;AACA,cAAI,KAAKxF,QAAL,IAAiB,CAAjB,IAAsB,KAAKJ,YAAL,KAAsBH,SAAS,CAACI,OAA1D,EAAmE;AAC/D,iBAAK+F,QAAL,CAAc,KAAd,EAD+D,CACzC;AACzB;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,gBAAgB,GAAG;AACtB,cAAI,KAAKzF,eAAT,EAA0B;AACtB,iBAAKJ,QAAL,GAAgB,KAAKI,eAAL,CAAqB0F,gBAArB,EAAhB;AACA,iBAAKN,sBAAL,GAFsB,CAItB;;AACA,gBAAI,KAAKxF,QAAL,IAAiB,CAAjB,IAAsB,KAAKJ,YAAL,KAAsBH,SAAS,CAACI,OAA1D,EAAmE;AAC/D,mBAAK+F,QAAL,CAAc,KAAd,EAD+D,CACzC;AACzB;AACJ;AACJ;AAED;AACJ;AACA;;;AACWG,QAAAA,iBAAiB,GAAG;AACvB,cAAI,KAAK3F,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqB4F,cAArB,GADsB,CACiB;;AACvC,iBAAKhG,QAAL,GAAgB,KAAKC,WAArB;AACA,iBAAKuF,sBAAL;AACAhF,YAAAA,OAAO,CAACC,GAAR,CAAa,YAAW,KAAKT,QAAS,IAAG,KAAKC,WAAY,EAA1D;AACH;AACJ;AAED;AACJ;AACA;;;AACWuF,QAAAA,sBAAsB,GAAG;AAC5B,cAAI,KAAKS,eAAL,IAAwB,KAAKhG,WAAL,GAAmB,CAA/C,EAAkD;AAC9C,iBAAKgG,eAAL,CAAqBC,QAArB,GAAgC,KAAKlG,QAAL,GAAgB,KAAKC,WAArD;AACH;AACJ;AAED;AACJ;AACA;;;AACW8C,QAAAA,iBAAiB,CAACoD,KAAD,EAAgB;AACpC,eAAKjG,UAAL,GAAkBiG,KAAlB;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBC,MAArB,GAA+B,SAAQ,KAAKnG,UAAW,EAAvD;AACH,WAJmC,CAMpC;;;AACA,cAAI,KAAKA,UAAL,IAAmB,CAAnB,IAAwB,KAAKN,YAAL,KAAsBH,SAAS,CAACI,OAAxD,IAAmE,KAAKM,iBAAL,GAAyB,CAAhG,EAAmG;AAC/F,iBAAKyF,QAAL,CAAc,IAAd,EAD+F,CAC1E;AACxB;AACJ,SAtUsC,CAwUvC;;AAEA;AACJ;AACA;;;AACWlE,QAAAA,SAAS,GAAG;AACf,cAAI,KAAK9B,YAAL,KAAsBH,SAAS,CAACI,OAApC,EAA6C;AAE7C,eAAKD,YAAL,GAAoBH,SAAS,CAAC6G,MAA9B,CAHe,CAKf;;AACA1H,UAAAA,QAAQ,CAAC2H,KAAT,GANe,CAQf;;AACA,cAAI,KAAKpF,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAAyB,IAAzB;AACH;;AAEDZ,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACH;AAED;AACJ;AACA;;;AACWmB,QAAAA,UAAU,GAAG;AAChB,cAAI,KAAKhC,YAAL,KAAsBH,SAAS,CAAC6G,MAApC,EAA4C;AAE5C,eAAK1G,YAAL,GAAoBH,SAAS,CAACI,OAA9B,CAHgB,CAKhB;;AACAjB,UAAAA,QAAQ,CAAC4H,MAAT,GANgB,CAQhB;;AACA,cAAI,KAAKrF,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACH;;AAEDZ,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACWmF,QAAAA,QAAQ,CAACa,SAAD,EAAqB;AAChC,cAAI,KAAK7G,YAAL,KAAsBH,SAAS,CAACiH,SAApC,EAA+C;AAE/C,eAAK9G,YAAL,GAAoBH,SAAS,CAACiH,SAA9B;AACA,eAAK3G,WAAL,GAAmBkB,IAAI,CAACC,GAAL,EAAnB,CAJgC,CAMhC;;AACAtC,UAAAA,QAAQ,CAAC2H,KAAT,GAPgC,CAShC;;AACA,cAAI,KAAKlF,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBD,MAAnB,GAA4B,IAA5B;AACH,WAZ+B,CAchC;;;AACA,eAAKuF,gBAAL,CAAsBF,SAAtB,EAfgC,CAiBhC;;AACA,eAAKG,sBAAL,CAA4BH,SAA5B;AAEAjG,UAAAA,OAAO,CAACC,GAAR,CAAYgG,SAAS,GAAG,OAAH,GAAa,OAAlC;AACH;AAED;AACJ;AACA;;;AACWI,QAAAA,WAAW,GAAG;AACjB;AACAjI,UAAAA,QAAQ,CAAC4H,MAAT,GAFiB,CAIjB;;AACA;AAAA;AAAA,wCAAWM,SAAX,CAAqBlI,QAAQ,CAACmI,QAAT,GAAqBxC,IAA1C;AACH;AAED;AACJ;AACA;;;AACWzC,QAAAA,gBAAgB,GAAG;AACtB;AACAlD,UAAAA,QAAQ,CAAC4H,MAAT,GAFsB,CAItB;;AACA;AAAA;AAAA,wCAAWM,SAAX,CAAqB,aAArB;AACH;AAED;AACJ;AACA;;;AACYH,QAAAA,gBAAgB,CAACF,SAAD,EAAqB;AACzC;AACA,cAAI,KAAKO,kBAAT,EAA6B;AACzB,iBAAKA,kBAAL,CAAwBX,MAAxB,GAAiCI,SAAS,GAAG,KAAH,GAAW,KAArD;AACH;AACJ;AAED;AACJ;AACA;;;AACYG,QAAAA,sBAAsB,CAACH,SAAD,EAAqB;AAC/C;AACA,gBAAMQ,UAAU,GAAG,KAAKlH,WAAL,GAAmB,KAAKD,aAA3C;AACA,gBAAMoH,WAAW,GAAGD,UAAU,GAAG,IAAjC;;AAEA,cAAI,CAACR,SAAL,EAAgB;AACZ;AACA,iBAAKU,cAAL,CAAoB,IAApB,EAA0B,EAA1B;AACA;AAAA;AAAA,gDAAcC,QAAd,CAAuBC,QAAvB,CAAgC,EAAhC,EAHY,CAKZ;;AACA,iBAAKC,mBAAL,CAAyBL,UAAzB,EAAqC,CAArC;AACA;AACH,WAb8C,CAe/C;;;AACA,gBAAMM,gBAAgB,GAAG,KAAKvH,QAAL,GAAgB,KAAKC,WAA9C,CAhB+C,CAkB/C;;AACA,gBAAMuH,KAAK,GAAG,KAAKC,cAAL,CAAoBP,WAApB,EAAiCK,gBAAjC,CAAd,CAnB+C,CAqB/C;;AACA,gBAAM;AAAEG,YAAAA,WAAF;AAAeC,YAAAA;AAAf,cAA0B,KAAKC,oBAAL,CAA0BV,WAA1B,EAAuCK,gBAAvC,CAAhC,CAtB+C,CAwB/C;;AACA,eAAKJ,cAAL,CAAoBO,WAApB,EAAiCC,MAAjC,EAzB+C,CA2B/C;;AACA;AAAA;AAAA,8CAAcP,QAAd,CAAuBC,QAAvB,CAAgCM,MAAhC,EA5B+C,CA8B/C;;AACA,eAAKL,mBAAL,CAAyBL,UAAzB,EAAqCO,KAArC;AAEAhH,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQyG,WAAW,CAACW,OAAZ,CAAoB,CAApB,CAAuB,WAAU,CAACN,gBAAgB,GAAG,GAApB,EAAyBM,OAAzB,CAAiC,CAAjC,CAAoC,UAASL,KAAM,SAAQE,WAAY,SAAQC,MAAO,IAA5I;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,cAAc,CAACK,QAAD,EAAmBP,gBAAnB,EAAqD;AACvE,cAAIC,KAAK,GAAG,CAAZ,CADuE,CACxD;AAEf;;AACA,cAAIM,QAAQ,IAAI,EAAhB,EAAoB;AAChBN,YAAAA,KAAK,GADW,CACP;AACZ,WANsE,CAQvE;;;AACA,cAAID,gBAAgB,IAAI,GAAxB,EAA6B;AACzBC,YAAAA,KAAK,GADoB,CAChB;AACZ;;AAED,iBAAOlE,IAAI,CAACyE,GAAL,CAASP,KAAT,EAAgB,CAAhB,CAAP,CAbuE,CAa5C;AAC9B;AAED;AACJ;AACA;;;AACYF,QAAAA,mBAAmB,CAACL,UAAD,EAAqBO,KAArB,EAAoC;AAC3D;AACA,gBAAMQ,cAAc,GAAG;AAAA;AAAA,oCAAS/F,aAAhC;;AACA,cAAI,CAAC+F,cAAL,EAAqB;AACjBxH,YAAAA,OAAO,CAACyH,IAAR,CAAa,YAAb;AACA;AACH,WAN0D,CAQ3D;;;AACA;AAAA;AAAA,8CAAcb,QAAd,CAAuBE,mBAAvB,CAA2CU,cAA3C,EAA2Df,UAA3D,EAAuEO,KAAvE;AAEAhH,UAAAA,OAAO,CAACC,GAAR,CAAa,YAAWuH,cAAe,SAAQf,UAAW,WAAUO,KAAM,EAA1E;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,oBAAoB,CAACE,QAAD,EAAmBP,gBAAnB,EAAsF;AAC9G,cAAIW,KAAK,GAAG,CAAZ,CAD8G,CAG9G;;AACA,cAAIJ,QAAQ,IAAI,EAAhB,EAAoB;AAChBI,YAAAA,KAAK,IAAI,EAAT,CADgB,CACH;AAChB,WAFD,MAEO,IAAIJ,QAAQ,IAAI,EAAhB,EAAoB;AACvBI,YAAAA,KAAK,IAAI,EAAT,CADuB,CACV;AAChB,WAFM,MAEA,IAAIJ,QAAQ,IAAI,EAAhB,EAAoB;AACvBI,YAAAA,KAAK,IAAI,EAAT,CADuB,CACV;AAChB,WAFM,MAEA,IAAIJ,QAAQ,IAAI,GAAhB,EAAqB;AACxBI,YAAAA,KAAK,IAAI,EAAT,CADwB,CACX;AAChB,WAFM,MAEA;AACHA,YAAAA,KAAK,IAAI,EAAT,CADG,CACU;AAChB,WAd6G,CAgB9G;;;AACAA,UAAAA,KAAK,IAAI5E,IAAI,CAACC,KAAL,CAAWgE,gBAAgB,GAAG,EAA9B,CAAT,CAjB8G,CAmB9G;;AACA,cAAIG,WAAJ;AACA,cAAIC,MAAJ;;AAEA,cAAIO,KAAK,IAAI,EAAb,EAAiB;AACbR,YAAAA,WAAW,GAAG,WAAd;AACAC,YAAAA,MAAM,GAAG,GAAT;AACH,WAHD,MAGO,IAAIO,KAAK,IAAI,EAAb,EAAiB;AACpBR,YAAAA,WAAW,GAAG,WAAd;AACAC,YAAAA,MAAM,GAAG,GAAT;AACH,WAHM,MAGA,IAAIO,KAAK,IAAI,EAAb,EAAiB;AACpBR,YAAAA,WAAW,GAAG,WAAd;AACAC,YAAAA,MAAM,GAAG,GAAT;AACH,WAHM,MAGA,IAAIO,KAAK,IAAI,EAAb,EAAiB;AACpBR,YAAAA,WAAW,GAAG,WAAd;AACAC,YAAAA,MAAM,GAAG,GAAT;AACH,WAHM,MAGA;AACHD,YAAAA,WAAW,GAAG,WAAd;AACAC,YAAAA,MAAM,GAAG,GAAT;AACH;;AAED,iBAAO;AAAED,YAAAA,WAAF;AAAeC,YAAAA;AAAf,WAAP;AACH;AAED;AACJ;AACA;;;AACYR,QAAAA,cAAc,CAACO,WAAD,EAAsBC,MAAtB,EAAsC;AACxD,cAAI,KAAKQ,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsB9B,MAAtB,GAAgC,SAAQqB,WAAY,EAApD;AACH;;AAED,cAAI,KAAKU,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiB/B,MAAjB,GAA2B,SAAQsB,MAAO,EAA1C;AACH;AACJ,SA/iBsC,CAijBvC;;AAEA;AACJ;AACA;;;AACWU,QAAAA,eAAe,GAAc;AAChC,iBAAO,KAAKzI,YAAZ;AACH;AAED;AACJ;AACA;;;AACW0I,QAAAA,WAAW,GAAW;AACzB,cAAI,KAAKvI,WAAL,GAAmB,CAAvB,EAA0B;AACtB,mBAAO,CAAC,KAAKA,WAAL,GAAmB,KAAKD,aAAzB,IAA0C,IAAjD;AACH;;AACD,iBAAO,CAACmB,IAAI,CAACC,GAAL,KAAa,KAAKpB,aAAnB,IAAoC,IAA3C;AACH;AAED;AACJ;AACA;;;AACWyI,QAAAA,WAAW,GAAW;AACzB,iBAAO,KAAKvI,QAAZ;AACH;AAED;AACJ;AACA;;;AACWwI,QAAAA,cAAc,GAAW;AAC5B,iBAAO,KAAKvI,WAAZ;AACH;AAED;AACJ;AACA;;;AACWwI,QAAAA,aAAa,GAAW;AAC3B,iBAAO,KAAKvI,UAAZ;AACH;;AAvlBsC,O,UA8CxBI,S,GAAyB,I;;;;;iBA5CrB,I;;;;;;;iBAGJ,I;;;;;;;iBAGI,I;;;;;;;iBAGJ,I;;;;;;;iBAGgB,I;;;;;;;iBAGN,I;;;;;;;iBAGH,I;;;;;;;iBAGH,I;;;;;;;iBAGG,I;;;;;;;iBAGC,I;;;;;;;iBAGG,I;;;;;;;iBAGD,I;;;;;;;iBAGG,I;;;;;;;iBAGF,I;;;;;;;iBAGL,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, instantiate, resources, UITransform, director, ProgressBar, Label, Button } from 'cc';\nimport { TempData } from './TempData';\nimport { CameraFollow } from './camera_follow';\nimport { player } from './player';\nimport { AIController } from './AIController';\nimport { AIPlayer } from './AIPlayer';\nimport { PlayerManager } from './PlayerManager';\nimport { SceneFader } from './SceneFader';\nconst { ccclass, property } = _decorator;\n\n// 游戏状态枚举\nexport enum GameState {\n    RUNNING = 'running',    // 游戏运行中\n    PAUSED = 'paused',      // 游戏暂停\n    GAME_OVER = 'game_over' // 游戏结束\n}\n\n@ccclass('GameManager')\nexport class GameManager extends Component {\n    @property(Node)\n    playGround: Node = null!; // PlayGround节点\n\n    @property(Node)\n    canvas: Node = null!; // Canvas节点\n\n    @property(Node)\n    spawnPoint: Node = null!; // SpawnPoint节点\n\n    @property(Node)\n    camera: Node = null!; // Camera节点\n\n    @property(ProgressBar)\n    playerHealthBar: ProgressBar = null!; // 玩家血量条\n\n    @property(Label)\n    enemyCountLabel: Label = null!; // 敌人数量Label\n\n    @property(Button)\n    pauseButton: Button = null!; // 暂停按钮\n\n    @property(Node)\n    pausePanel: Node = null!; // 暂停面板\n\n    @property(Node)\n    gameOverPanel: Node = null!; // 游戏结束面板\n\n    @property(Button)\n    resumeButton: Button = null!; // 继续游戏按钮\n\n    @property(Button)\n    mainMenuButton2: Button = null!; // 重新开始按钮\n\n    @property(Button)\n    mainMenuButton: Button = null!; // 返回主菜单按钮\n\n    @property(Label)\n    gameOverTitleLabel: Label = null!; // 游戏结束标题\n\n    @property(Label)\n    performanceLabel: Label = null!; // 表现评价标签\n\n    @property(Label)\n    rewardLabel: Label = null!; // 奖励金币标签\n\n    private static _instance: GameManager = null!;\n    private aiPlayers: AIPlayer[] = [];\n\n    // 游戏状态相关\n    private currentState: GameState = GameState.RUNNING;\n    private gameStartTime: number = 0;\n    private gameEndTime: number = 0;\n\n    // 玩家数据\n    private playerHP: number = 0; // 将在player加载完成后初始化\n    private playerMaxHP: number = 0; // 将在player加载完成后初始化\n    private enemyCount: number = 0;\n    private initialEnemyCount: number = 0;\n    private playerComponent: player | null = null; // 玩家组件引用\n\n    public static getInstance(): GameManager {\n        return GameManager._instance;\n    }\n\n    onLoad() {\n        if (GameManager._instance) {\n            console.log(\"销毁原有单例\")\n            this.node.destroy();\n            return;\n        }\n        GameManager._instance = this;\n    }\n\n    start() {\n        console.log(\"调用场景内容加载\")\n        this.initializeGame();\n        this.bindButtonEvents();\n        this.loadLevelAndCar();\n    }\n    onDestroy() {\n         if (GameManager._instance === this) {\n            GameManager._instance = null;\n            console.log(\"GameManager 实例已销毁\");\n        }\n}\n\n    /**\n     * 初始化游戏\n     */\n    private initializeGame() {\n        this.currentState = GameState.RUNNING;\n        this.gameStartTime = Date.now();\n        this.gameEndTime = 0;\n\n        // 初始化UI面板状态\n        if (this.pausePanel) {\n            this.pausePanel.active = false;\n        }\n        if (this.gameOverPanel) {\n            this.gameOverPanel.active = false;\n        }\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    private bindButtonEvents() {\n        // 暂停按钮\n        if (this.pauseButton) {\n            this.pauseButton.node.on(Button.EventType.CLICK, this.pauseGame, this);\n        }\n\n        // 继续游戏按钮\n        if (this.resumeButton) {\n            this.resumeButton.node.on(Button.EventType.CLICK, this.resumeGame, this);\n        }\n\n        // 重新开始按钮\n        if (this.mainMenuButton2) {\n            this.mainMenuButton2.node.on(Button.EventType.CLICK, this.returnToMainMenu, this);\n        }\n\n        // 返回主菜单按钮\n        if (this.mainMenuButton) {\n            this.mainMenuButton.node.on(Button.EventType.CLICK, this.returnToMainMenu, this);\n        }\n    }\n\n    loadLevelAndCar() {\n        const levelId = TempData.selectedLevel;\n        const carId = TempData.selectedCar;\n        let mapNode: Node | null = null;\n        let playerNode: Node | null = null;\n        // 1. 加载并实例化场景背景\n        if (levelId) {\n            resources.load(`prefab/levels/${levelId}`, Prefab, (err, prefab) => {\n                if (!err && prefab) {\n                    mapNode = instantiate(prefab);\n                    mapNode.setPosition(0, 0, 0);\n                    this.playGround.addChild(mapNode);\n                    \n                    // 场景预制体加载完成，查找AI车辆\n                    this.autoFindAIPlayers();\n                    this.notifyAIControllers();\n\n                    // 初始化敌人数量\n                    this.initialEnemyCount = this.aiPlayers.length;\n                    this.refreshEnemyCount(this.initialEnemyCount);\n                    \n                    // 2. 加载并实例化车辆\n                    if (carId) {\n                        resources.load(`prefab/cars/${carId}`, Prefab, (err2, prefab2) => {\n                            if (!err2 && prefab2) {\n                                playerNode = instantiate(prefab2);\n                                // 随机选择一个SpawnPoint的子节点\n                                const spawnChildren = this.spawnPoint.children;\n                                if (spawnChildren.length > 0) {\n                                    const randomIndex = Math.floor(Math.random() * spawnChildren.length);\n                                    const spawnNode = spawnChildren[randomIndex];\n                                    const spawnPos = spawnNode.getWorldPosition();\n                                    // 转换为Canvas的本地坐标\n                                    const localPos = this.canvas.getComponent(UITransform).convertToNodeSpaceAR(spawnPos);\n                                    playerNode.setPosition(localPos);\n                                    playerNode.setRotation(spawnNode.getRotation());\n                                    // 设置初始角度并初始化玩家血量\n                                    const playerScript = playerNode.getComponent(player);\n                                    if (playerScript) {\n                                        playerScript.init(spawnNode.angle);\n                                        // 初始化玩家血量数据\n                                        this.initializePlayerHealth(playerScript);\n                                    }\n                                    // 根据点位名称设置朝向\n                                    if ([\"point4\", \"point5\", \"point6\"].indexOf(spawnNode.name) !== -1) {\n                                        console.log(\"生成车辆在右侧\")\n                                        // playerNode.setRotationFromEuler(0, 0, 90);\n                                    } \n                                } \n                                this.canvas.addChild(playerNode);\n                                // 3. 通知相机\n                                const cameraFollow = this.camera.getComponent(CameraFollow);\n                                if (cameraFollow && mapNode && playerNode) {\n                                    cameraFollow.init(mapNode, playerNode);\n                                }\n                            }\n                            if (err2) {\n                                console.error('加载车辆预制体失败:', err2, carId);\n                                return;\n                            }\n                            if (!prefab2) {\n                                console.error('未找到车辆预制体:', carId);\n                                return;\n                            }\n                        });\n                    }\n                }\n                if (err) {\n                    console.error('加载关卡预制体失败:', err, levelId);\n                    return;\n                }\n                if (!prefab) {\n                    console.error('未找到关卡预制体:', levelId);\n                    return;\n                }\n            });\n        }\n    }\n\n    /**\n     * 查找所有AIPlayer组件\n     */\n    public autoFindAIPlayers() {\n        this.aiPlayers = [];\n        // 路径: Canvas → PlayGround → 场景预制体 → cars\n        const scene = this.node.scene;\n        if (!scene) return;\n        const canvas = scene.getChildByName('Canvas');\n        if (!canvas) return;\n        const playGround = canvas.getChildByName('PlayGround');\n        if (!playGround) return;\n        const sceneNode = playGround.children[0];\n        if (!sceneNode) return;\n        const carsNode = sceneNode.getChildByName('cars');\n        if (!carsNode) return;\n        for (const carNode of carsNode.children) {\n            const aiPlayer = carNode.getComponent(AIPlayer);\n            if (aiPlayer) {\n                this.aiPlayers.push(aiPlayer);\n            }\n        }\n    }\n\n    /**\n     * 获取AI车辆列表\n     */\n    public getAIPlayers(): AIPlayer[] {\n        return this.aiPlayers;\n    }\n\n    /**\n     * 通知所有AIController组件场景预制体已加载完成\n     */\n    private notifyAIControllers() {\n        const aiControllers = this.node.scene.getComponentsInChildren(AIController);\n        for (const aiController of aiControllers) {\n            aiController.onScenePrefabLoaded();\n        }\n    }\n\n    /**\n     * 初始化玩家血量数据\n     */\n    private initializePlayerHealth(playerScript: player) {\n        this.playerComponent = playerScript;\n        this.playerMaxHP = playerScript.getMaxHealth();\n        this.playerHP = this.playerMaxHP;\n\n        console.log(`玩家血量初始化完成: ${this.playerHP}/${this.playerMaxHP}`);\n\n        // 刷新血量UI\n        this.refreshPlayerHealthBar();\n    }\n\n    /**\n     * 减少玩家血量并刷新UI\n     */\n    public reducePlayerHP(amount: number) {\n        console.log('减少玩家血量:', amount);\n        this.playerHP = Math.max(0, this.playerHP - amount);\n        this.refreshPlayerHealthBar();\n\n        // 检查玩家是否死亡\n        if (this.playerHP <= 0 && this.currentState === GameState.RUNNING) {\n            this.gameOver(false); // 玩家死亡，游戏失败\n        }\n    }\n\n    /**\n     * 同步玩家血量（从player组件获取最新血量）\n     */\n    public syncPlayerHealth() {\n        if (this.playerComponent) {\n            this.playerHP = this.playerComponent.getCurrentHealth();\n            this.refreshPlayerHealthBar();\n\n            // 检查玩家是否死亡\n            if (this.playerHP <= 0 && this.currentState === GameState.RUNNING) {\n                this.gameOver(false); // 玩家死亡，游戏失败\n            }\n        }\n    }\n\n    /**\n     * 重置玩家血量到满血状态\n     */\n    public resetPlayerHealth() {\n        if (this.playerComponent) {\n            this.playerComponent.restoreVehicle(); // 恢复玩家车辆状态\n            this.playerHP = this.playerMaxHP;\n            this.refreshPlayerHealthBar();\n            console.log(`玩家血量已重置: ${this.playerHP}/${this.playerMaxHP}`);\n        }\n    }\n\n    /**\n     * 刷新玩家血量进度条\n     */\n    public refreshPlayerHealthBar() {\n        if (this.playerHealthBar && this.playerMaxHP > 0) {\n            this.playerHealthBar.progress = this.playerHP / this.playerMaxHP;\n        }\n    }\n\n    /**\n     * 刷新剩余敌人数量并刷新UI\n     */\n    public refreshEnemyCount(count: number) {\n        this.enemyCount = count;\n        if (this.enemyCountLabel) {\n            this.enemyCountLabel.string = `敌人剩余: ${this.enemyCount}`;\n        }\n\n        // 检查是否所有敌人都被消灭\n        if (this.enemyCount <= 0 && this.currentState === GameState.RUNNING && this.initialEnemyCount > 0) {\n            this.gameOver(true); // 敌人全部消灭，游戏胜利\n        }\n    }\n\n    // ==================== 游戏状态管理方法 ====================\n\n    /**\n     * 暂停游戏\n     */\n    public pauseGame() {\n        if (this.currentState !== GameState.RUNNING) return;\n\n        this.currentState = GameState.PAUSED;\n\n        // 暂停游戏时间\n        director.pause();\n\n        // 显示暂停面板\n        if (this.pausePanel) {\n            this.pausePanel.active = true;\n        }\n\n        console.log('游戏已暂停');\n    }\n\n    /**\n     * 继续游戏\n     */\n    public resumeGame() {\n        if (this.currentState !== GameState.PAUSED) return;\n\n        this.currentState = GameState.RUNNING;\n\n        // 恢复游戏时间\n        director.resume();\n\n        // 隐藏暂停面板\n        if (this.pausePanel) {\n            this.pausePanel.active = false;\n        }\n\n        console.log('游戏已继续');\n    }\n\n    /**\n     * 游戏结束\n     * @param isVictory 是否胜利\n     */\n    public gameOver(isVictory: boolean) {\n        if (this.currentState === GameState.GAME_OVER) return;\n\n        this.currentState = GameState.GAME_OVER;\n        this.gameEndTime = Date.now();\n\n        // 暂停游戏\n        director.pause();\n\n        // 显示游戏结束面板\n        if (this.gameOverPanel) {\n            this.gameOverPanel.active = true;\n        }\n\n        // 更新游戏结束UI\n        this.updateGameOverUI(isVictory);\n\n        // 计算并给予奖励\n        this.calculateAndGiveReward(isVictory);\n\n        console.log(isVictory ? '游戏胜利！' : '游戏失败！');\n    }\n\n    /**\n     * 重新开始游戏\n     */\n    public restartGame() {\n        // 恢复游戏时间\n        director.resume();\n\n        // 使用渐变效果重新加载当前场景\n        SceneFader.loadScene(director.getScene()!.name);\n    }\n\n    /**\n     * 返回主菜单\n     */\n    public returnToMainMenu() {\n        // 恢复游戏时间\n        director.resume();\n\n        // 使用渐变效果加载主菜单场景\n        SceneFader.loadScene('LevelSelect');\n    }\n\n    /**\n     * 更新游戏结束UI\n     */\n    private updateGameOverUI(isVictory: boolean) {\n        // 更新标题\n        if (this.gameOverTitleLabel) {\n            this.gameOverTitleLabel.string = isVictory ? '胜利！' : '失败！';\n        }\n    }\n\n    /**\n     * 计算表现评价和奖励\n     */\n    private calculateAndGiveReward(isVictory: boolean) {\n        // 计算游戏时长（毫秒）\n        const gameTimeMs = this.gameEndTime - this.gameStartTime;\n        const gameTimeSec = gameTimeMs / 1000;\n\n        if (!isVictory) {\n            // 失败时记录F级评价，不给奖励\n            this.updateRewardUI('失败', 10);\n            PlayerManager.instance.addMoney(10);\n\n            // 记录失败的关卡进度（0星，F级）\n            this.updateLevelProgress(gameTimeMs, 0);\n            return;\n        }\n\n        // 计算生命值百分比\n        const healthPercentage = this.playerHP / this.playerMaxHP;\n\n        // 计算星星数（基于生命值和时间）\n        const stars = this.calculateStars(gameTimeSec, healthPercentage);\n\n        // 计算表现评价和奖励\n        const { performance, reward } = this.calculatePerformance(gameTimeSec, healthPercentage);\n\n        // 更新UI显示\n        this.updateRewardUI(performance, reward);\n\n        // 给予玩家奖励\n        PlayerManager.instance.addMoney(reward);\n\n        // 更新关卡进度\n        this.updateLevelProgress(gameTimeMs, stars);\n\n        console.log(`游戏时长: ${gameTimeSec.toFixed(1)}秒, 生命值: ${(healthPercentage * 100).toFixed(1)}%, 星星: ${stars}, 评价: ${performance}, 奖励: ${reward}金币`);\n    }\n\n    /**\n     * 计算星星数\n     */\n    private calculateStars(gameTime: number, healthPercentage: number): number {\n        let stars = 1; // 基础1星（完成关卡）\n\n        // 基于时间加星\n        if (gameTime <= 60) {\n            stars++; // 60秒内完成 +1星\n        }\n\n        // 基于生命值加星\n        if (healthPercentage >= 0.5) {\n            stars++; // 生命值50%以上 +1星\n        }\n\n        return Math.min(stars, 3); // 最多3星\n    }\n\n    /**\n     * 更新关卡进度\n     */\n    private updateLevelProgress(gameTimeMs: number, stars: number) {\n        // 获取当前关卡ID\n        const currentLevelId = TempData.selectedLevel;\n        if (!currentLevelId) {\n            console.warn('无法获取当前关卡ID');\n            return;\n        }\n\n        // 更新PlayerManager中的关卡进度\n        PlayerManager.instance.updateLevelProgress(currentLevelId, gameTimeMs, stars);\n\n        console.log(`关卡进度已更新: ${currentLevelId}, 时间: ${gameTimeMs}ms, 星星: ${stars}`);\n    }\n\n    /**\n     * 计算表现评价\n     */\n    private calculatePerformance(gameTime: number, healthPercentage: number): { performance: string, reward: number } {\n        let score = 0;\n\n        // 时间评分 (0-50分)\n        if (gameTime <= 30) {\n            score += 50; // 30秒内完成，满分\n        } else if (gameTime <= 60) {\n            score += 40; // 60秒内完成，40分\n        } else if (gameTime <= 90) {\n            score += 30; // 90秒内完成，30分\n        } else if (gameTime <= 120) {\n            score += 20; // 120秒内完成，20分\n        } else {\n            score += 10; // 超过120秒，10分\n        }\n\n        // 生命值评分 (0-50分)\n        score += Math.floor(healthPercentage * 50);\n\n        // 根据总分确定评价和奖励\n        let performance: string;\n        let reward: number;\n\n        if (score >= 90) {\n            performance = 'S级 - 完美表现';\n            reward = 500;\n        } else if (score >= 80) {\n            performance = 'A级 - 优秀表现';\n            reward = 400;\n        } else if (score >= 70) {\n            performance = 'B级 - 良好表现';\n            reward = 300;\n        } else if (score >= 60) {\n            performance = 'C级 - 一般表现';\n            reward = 200;\n        } else {\n            performance = 'D级 - 需要改进';\n            reward = 100;\n        }\n\n        return { performance, reward };\n    }\n\n    /**\n     * 更新奖励UI显示\n     */\n    private updateRewardUI(performance: string, reward: number) {\n        if (this.performanceLabel) {\n            this.performanceLabel.string = `表现评价: ${performance}`;\n        }\n\n        if (this.rewardLabel) {\n            this.rewardLabel.string = `获得金币: ${reward}`;\n        }\n    }\n\n    // ==================== 公共方法 ====================\n\n    /**\n     * 获取当前游戏状态\n     */\n    public getCurrentState(): GameState {\n        return this.currentState;\n    }\n\n    /**\n     * 获取游戏时长（秒）\n     */\n    public getGameTime(): number {\n        if (this.gameEndTime > 0) {\n            return (this.gameEndTime - this.gameStartTime) / 1000;\n        }\n        return (Date.now() - this.gameStartTime) / 1000;\n    }\n\n    /**\n     * 获取玩家当前生命值\n     */\n    public getPlayerHP(): number {\n        return this.playerHP;\n    }\n\n    /**\n     * 获取玩家最大生命值\n     */\n    public getPlayerMaxHP(): number {\n        return this.playerMaxHP;\n    }\n\n    /**\n     * 获取剩余敌人数量\n     */\n    public getEnemyCount(): number {\n        return this.enemyCount;\n    }\n}"]}