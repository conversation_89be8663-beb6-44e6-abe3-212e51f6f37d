{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "SceneFader", "ccclass", "property", "MainMenuController", "start", "startGameBtn", "node", "on", "EventType", "CLICK", "onStartGame", "loadScene"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;;AACvBC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;oCAGjBM,kB,WADZF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAACH,MAAD,C,2BAFb,MACaI,kBADb,SACwCL,SADxC,CACkD;AAAA;AAAA;;AAAA;AAAA;;AAEhB;AAE9BM,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBC,IAAlB,CAAuBC,EAAvB,CAA0BR,MAAM,CAACS,SAAP,CAAiBC,KAA3C,EAAkD,KAAKC,WAAvD,EAAoE,IAApE;AACH;AACJ;;AAEDA,QAAAA,WAAW,GAAG;AACV;AACA;AAAA;AAAA,wCAAWC,SAAX,CAAqB,aAArB,EAFU,CAGV;AACH;;AAd6C,O;;;;;iBAEvB,I", "sourcesContent": ["import { _decorator, Component, Button } from 'cc';\nimport { SceneFader } from './SceneFader';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MainMenuController')\nexport class MainMenuController extends Component {\n    @property(Button)\n    startGameBtn: Button = null!; // 拖拽你的\"开始游戏\"按钮到这里\n\n    start() {\n        if (this.startGameBtn) {\n            this.startGameBtn.node.on(Button.EventType.CLICK, this.onStartGame, this);\n        }\n    }\n\n    onStartGame() {\n        // 使用渐变效果切换到关卡选择场景\n        SceneFader.loadScene(\"LevelSelect\");\n        // director.loadScene(\"gamescene\");\n    }\n} "]}