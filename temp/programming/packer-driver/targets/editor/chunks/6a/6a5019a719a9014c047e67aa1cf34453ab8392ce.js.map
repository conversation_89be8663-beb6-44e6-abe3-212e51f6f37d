{"version": 3, "sources": ["cce:/internal/code-quality/cr.mjs"], "names": ["report", "imported", "moduleRequest", "importMeta", "extras", "console", "warn", "url", "error"], "mappings": ";;;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEO,WAASA,MAAT,CAAgBC,QAAhB,EAA0BC,aAA1B,EAAyCC,UAAzC,EAAqDC,MAArD,EAA6D;AAChEC,IAAAA,OAAO,CAACC,IAAR,CACK,yCAAwCH,UAAU,CAACI,GAAI;AAChE,qBAAqBN,QAAS,oBAAmBC,aAAc;AAC/D,CAHI,EAIIE,MAAM,CAACI,KAJX;AAMH;;oBAPeR,M", "sourcesContent": ["\n/**\n * This is the module which implements circular-reference detection.\n */\n\n/**\n * Reports a circular reference error fired by module import.\n * @param imported The binding of the import.\n * @param moduleRequest The module request of the import.\n * @param importMeta The import.meta of the source module.\n * @param extras Extra data passed by circular reference detection implementation.\n */\n\nexport function report(imported, moduleRequest, importMeta, extras) {\n    console.warn(\n        `Found possible circular reference in \"${importMeta.url}\", \\\nhappened when use \"${imported}\" imported from \"${moduleRequest}\" \\\n`,\n        extras.error,\n    );\n}\n"]}