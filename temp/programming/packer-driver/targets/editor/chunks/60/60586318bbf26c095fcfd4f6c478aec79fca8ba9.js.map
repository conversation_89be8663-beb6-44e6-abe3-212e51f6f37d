{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Label", "GameManager", "ccclass", "property", "GameOverPanel", "start", "bindButtonEvents", "updateGameStats", "restartButton", "node", "on", "EventType", "CLICK", "onRestartClick", "mainMenuButton", "onMainMenuClick", "gameManager", "getInstance", "gameTimeLabel", "gameTime", "getGameTime", "string", "toFixed", "healthLabel", "playerHP", "getPlayerHP", "maxHP", "getPlayerMaxHP", "healthPercentage", "restartGame", "returnToMainMenu", "setGameOverInfo", "isVictory", "performance", "reward", "titleLabel", "performance<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AACrCC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;+BAGjBO,a,WADZF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACH,KAAD,C,UAGRG,QAAQ,CAACH,KAAD,C,UAGRG,QAAQ,CAACH,KAAD,C,UAGRG,QAAQ,CAACH,KAAD,C,UAGRG,QAAQ,CAACH,KAAD,C,UAGRG,QAAQ,CAACJ,MAAD,C,UAGRI,QAAQ,CAACJ,MAAD,C,2BApBb,MACaK,aADb,SACmCN,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAEd;AAFc;;AAKR;AALQ;;AAQb;AARa;;AAWX;AAXW;;AAcb;AAda;;AAiBV;AAjBU;AAAA;;AAoBT;AAEhCO,QAAAA,KAAK,GAAG;AACJ,eAAKC,gBAAL;AACA,eAAKC,eAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,gBAAgB,GAAG;AACvB,cAAI,KAAKE,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,IAAnB,CAAwBC,EAAxB,CAA2BX,MAAM,CAACY,SAAP,CAAiBC,KAA5C,EAAmD,KAAKC,cAAxD,EAAwE,IAAxE;AACH;;AAED,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBL,IAApB,CAAyBC,EAAzB,CAA4BX,MAAM,CAACY,SAAP,CAAiBC,KAA7C,EAAoD,KAAKG,eAAzD,EAA0E,IAA1E;AACH;AACJ;AAED;AACJ;AACA;;;AACYR,QAAAA,eAAe,GAAG;AACtB,gBAAMS,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;AACA,cAAI,CAACD,WAAL,EAAkB,OAFI,CAItB;;AACA,cAAI,KAAKE,aAAT,EAAwB;AACpB,kBAAMC,QAAQ,GAAGH,WAAW,CAACI,WAAZ,EAAjB;AACA,iBAAKF,aAAL,CAAmBG,MAAnB,GAA6B,SAAQF,QAAQ,CAACG,OAAT,CAAiB,CAAjB,CAAoB,GAAzD;AACH,WARqB,CAUtB;;;AACA,cAAI,KAAKC,WAAT,EAAsB;AAClB,kBAAMC,QAAQ,GAAGR,WAAW,CAACS,WAAZ,EAAjB;AACA,kBAAMC,KAAK,GAAGV,WAAW,CAACW,cAAZ,EAAd;AACA,kBAAMC,gBAAgB,GAAG,CAACJ,QAAQ,GAAGE,KAAX,GAAmB,GAApB,EAAyBJ,OAAzB,CAAiC,CAAjC,CAAzB;AACA,iBAAKC,WAAL,CAAiBF,MAAjB,GAA2B,UAASG,QAAS,IAAGE,KAAM,KAAIE,gBAAiB,IAA3E;AACH;AACJ;AAED;AACJ;AACA;;;AACYf,QAAAA,cAAc,GAAG;AACrB,gBAAMG,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACa,WAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYd,QAAAA,eAAe,GAAG;AACtB,gBAAMC,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACc,gBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,eAAe,CAACC,SAAD,EAAqBC,WAArB,EAA0CC,MAA1C,EAA0D;AAC5E,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBd,MAAhB,GAAyBW,SAAS,GAAG,KAAH,GAAW,KAA7C;AACH;;AAED,cAAI,KAAKI,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBf,MAAtB,GAAgC,SAAQY,WAAY,EAApD;AACH;;AAED,cAAI,KAAKI,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBhB,MAAjB,GAA2B,SAAQa,MAAO,EAA1C;AACH;AACJ;;AAEDI,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAK9B,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,IAAnB,CAAwB8B,GAAxB,CAA4BxC,MAAM,CAACY,SAAP,CAAiBC,KAA7C,EAAoD,KAAKC,cAAzD,EAAyE,IAAzE;AACH;;AACD,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBL,IAApB,CAAyB8B,GAAzB,CAA6BxC,MAAM,CAACY,SAAP,CAAiBC,KAA9C,EAAqD,KAAKG,eAA1D,EAA2E,IAA3E;AACH;AACJ;;AA3GwC,O;;;;;iBAErB,I;;;;;;;iBAGM,I;;;;;;;iBAGL,I;;;;;;;iBAGE,I;;;;;;;iBAGF,I;;;;;;;iBAGG,I;;;;;;;iBAGC,I", "sourcesContent": ["import { _decorator, Component, Node, Button, Label } from 'cc';\nimport { GameManager } from './GameManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('GameOverPanel')\nexport class GameOverPanel extends Component {\n    @property(Label)\n    titleLabel: Label = null!; // 游戏结束标题\n\n    @property(Label)\n    performanceLabel: Label = null!; // 表现评价标签\n\n    @property(Label)\n    rewardLabel: Label = null!; // 奖励金币标签\n\n    @property(Label)\n    gameTimeLabel: Label = null!; // 游戏时长标签\n\n    @property(Label)\n    healthLabel: Label = null!; // 剩余生命值标签\n\n    @property(Button)\n    restartButton: Button = null!; // 重新开始按钮\n\n    @property(Button)\n    mainMenuButton: Button = null!; // 返回主菜单按钮\n\n    start() {\n        this.bindButtonEvents();\n        this.updateGameStats();\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    private bindButtonEvents() {\n        if (this.restartButton) {\n            this.restartButton.node.on(Button.EventType.CLICK, this.onRestartClick, this);\n        }\n\n        if (this.mainMenuButton) {\n            this.mainMenuButton.node.on(Button.EventType.CLICK, this.onMainMenuClick, this);\n        }\n    }\n\n    /**\n     * 更新游戏统计信息\n     */\n    private updateGameStats() {\n        const gameManager = GameManager.getInstance();\n        if (!gameManager) return;\n\n        // 更新游戏时长\n        if (this.gameTimeLabel) {\n            const gameTime = gameManager.getGameTime();\n            this.gameTimeLabel.string = `游戏时长: ${gameTime.toFixed(1)}秒`;\n        }\n\n        // 更新剩余生命值\n        if (this.healthLabel) {\n            const playerHP = gameManager.getPlayerHP();\n            const maxHP = gameManager.getPlayerMaxHP();\n            const healthPercentage = (playerHP / maxHP * 100).toFixed(1);\n            this.healthLabel.string = `剩余生命值: ${playerHP}/${maxHP} (${healthPercentage}%)`;\n        }\n    }\n\n    /**\n     * 重新开始按钮点击\n     */\n    private onRestartClick() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.restartGame();\n        }\n    }\n\n    /**\n     * 返回主菜单按钮点击\n     */\n    private onMainMenuClick() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.returnToMainMenu();\n        }\n    }\n\n    /**\n     * 设置游戏结束信息\n     */\n    public setGameOverInfo(isVictory: boolean, performance: string, reward: number) {\n        if (this.titleLabel) {\n            this.titleLabel.string = isVictory ? '胜利！' : '失败！';\n        }\n\n        if (this.performanceLabel) {\n            this.performanceLabel.string = `表现评价: ${performance}`;\n        }\n\n        if (this.rewardLabel) {\n            this.rewardLabel.string = `获得金币: ${reward}`;\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        if (this.restartButton) {\n            this.restartButton.node.off(Button.EventType.CLICK, this.onRestartClick, this);\n        }\n        if (this.mainMenuButton) {\n            this.mainMenuButton.node.off(Button.EventType.CLICK, this.onMainMenuClick, this);\n        }\n    }\n}\n"]}