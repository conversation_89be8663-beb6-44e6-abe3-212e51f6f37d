{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts"], "names": ["_decorator", "Component", "KeyCode", "Input", "input", "<PERSON><PERSON><PERSON><PERSON>", "SelectManager", "ccclass", "property", "CarPurchaseTest", "onLoad", "on", "EventType", "KEY_DOWN", "onKeyDown", "console", "log", "onDestroy", "off", "event", "keyCode", "DIGIT_1", "addMoney", "DIGIT_2", "reduceMoney", "DIGIT_3", "testPurchaseCar", "DIGIT_4", "DIGIT_5", "resetPlayerData", "DIGIT_6", "showCurrentStatus", "amount", "<PERSON><PERSON><PERSON><PERSON>", "instance", "player<PERSON><PERSON>", "money", "success", "spendMoney", "carId", "error", "isCarUnlocked", "price", "selectManager", "getCarPrice", "onPurchaseCar", "updateCarToggles", "unlockedCars", "join", "carIds", "for<PERSON>ach", "isUnlocked"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AACvCC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;iCAEaS,e,WADZF,OAAO,CAAC,iBAAD,C,UAGHC,QAAQ;AAAA;AAAA,yC,2BAHb,MACaC,eADb,SACqCR,SADrC,CAC+C;AAAA;AAAA;;AAAA;AAAA;;AAK3CS,QAAAA,MAAM,GAAG;AACL;AACAN,UAAAA,KAAK,CAACO,EAAN,CAASR,KAAK,CAACS,SAAN,CAAgBC,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACRb,UAAAA,KAAK,CAACc,GAAN,CAAUf,KAAK,CAACS,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACH;;AAEOA,QAAAA,SAAS,CAACK,KAAD,EAAa;AAC1B,kBAAQA,KAAK,CAACC,OAAd;AACI,iBAAKlB,OAAO,CAACmB,OAAb;AACI,mBAAKC,QAAL,CAAc,GAAd;AACA;;AACJ,iBAAKpB,OAAO,CAACqB,OAAb;AACI,mBAAKC,WAAL,CAAiB,GAAjB;AACA;;AACJ,iBAAKtB,OAAO,CAACuB,OAAb;AACI,mBAAKC,eAAL,CAAqB,OAArB;AACA;;AACJ,iBAAKxB,OAAO,CAACyB,OAAb;AACI,mBAAKD,eAAL,CAAqB,OAArB;AACA;;AACJ,iBAAKxB,OAAO,CAAC0B,OAAb;AACI,mBAAKC,eAAL;AACA;;AACJ,iBAAK3B,OAAO,CAAC4B,OAAb;AACI,mBAAKC,iBAAL;AACA;AAlBR;AAoBH;AAED;AACJ;AACA;;;AACYT,QAAAA,QAAQ,CAACU,MAAD,EAAiB;AAC7B,cAAMC,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;;AACA,cAAID,aAAJ,EAAmB;AACfA,YAAAA,aAAa,CAACX,QAAd,CAAuBU,MAAvB;AACAjB,YAAAA,OAAO,CAACC,GAAR,mBAAkBgB,MAAlB,qDAAqCC,aAAa,CAACE,UAAd,CAAyBC,KAA9D,EAFe,CAIf;AACH;AACJ;AAED;AACJ;AACA;;;AACYZ,QAAAA,WAAW,CAACQ,MAAD,EAAiB;AAChC,cAAMC,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;;AACA,cAAID,aAAJ,EAAmB;AACf,gBAAMI,OAAO,GAAGJ,aAAa,CAACK,UAAd,CAAyBN,MAAzB,CAAhB;;AACA,gBAAIK,OAAJ,EAAa;AACTtB,cAAAA,OAAO,CAACC,GAAR,mBAAkBgB,MAAlB,qDAAqCC,aAAa,CAACE,UAAd,CAAyBC,KAA9D;AACH,aAFD,MAEO;AACHrB,cAAAA,OAAO,CAACC,GAAR,6DAAyBgB,MAAzB,qDAA4CC,aAAa,CAACE,UAAd,CAAyBC,KAArE;AACH,aANc,CAQf;;AACH;AACJ;AAED;AACJ;AACA;;;AACYV,QAAAA,eAAe,CAACa,KAAD,EAAgB;AACnC,cAAMN,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;;AACA,cAAI,CAACD,aAAL,EAAoB;AAChBlB,YAAAA,OAAO,CAACyB,KAAR,CAAc,qBAAd;AACA;AACH;;AAEDzB,UAAAA,OAAO,CAACC,GAAR,+CAA0BuB,KAA1B,WAPmC,CASnC;;AACA,cAAIN,aAAa,CAACQ,aAAd,CAA4BF,KAA5B,CAAJ,EAAwC;AACpCxB,YAAAA,OAAO,CAACC,GAAR,mBAAkBuB,KAAlB;AACA;AACH,WAbkC,CAenC;;;AACA,cAAMG,KAAK,GAAG,KAAKC,aAAL,GAAqB,KAAKA,aAAL,CAAmBC,WAAnB,CAA+BL,KAA/B,CAArB,GAA6D,CAA3E;AACAxB,UAAAA,OAAO,CAACC,GAAR,gCAAqB0B,KAArB;AACA3B,UAAAA,OAAO,CAACC,GAAR,gCAAqBiB,aAAa,CAACE,UAAd,CAAyBC,KAA9C,EAlBmC,CAoBnC;;AACA,cAAI,KAAKO,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBE,aAAnB,CAAiCN,KAAjC;AACH;AACJ;AAED;AACJ;AACA;;;AACYV,QAAAA,eAAe,GAAG;AACtB,cAAMI,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;;AACA,cAAID,aAAJ,EAAmB;AACfA,YAAAA,aAAa,CAACJ,eAAd;AACAd,YAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAFe,CAIf;;AACA,gBAAI,KAAK2B,aAAT,EAAwB;AACpB,mBAAKA,aAAL,CAAmBG,gBAAnB;AACH,aAPc,CAQf;;AACH;AACJ;AAED;AACJ;AACA;;;AACYf,QAAAA,iBAAiB,GAAG;AACxB,cAAME,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;;AACA,cAAI,CAACD,aAAL,EAAoB;AAChBlB,YAAAA,OAAO,CAACyB,KAAR,CAAc,qBAAd;AACA;AACH;;AAEDzB,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,oBAAmBiB,aAAa,CAACE,UAAd,CAAyBC,KAA5C;AACArB,UAAAA,OAAO,CAACC,GAAR,sCAAsBiB,aAAa,CAACE,UAAd,CAAyBY,YAAzB,CAAsCC,IAAtC,CAA2C,IAA3C,CAAtB,EATwB,CAWxB;;AACA,cAAI,KAAKL,aAAT,EAAwB;AACpB,gBAAMM,MAAM,GAAG,CAAC,OAAD,EAAU,OAAV,EAAmB,OAAnB,EAA4B,OAA5B,EAAqC,OAArC,CAAf;AACAlC,YAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACAiC,YAAAA,MAAM,CAACC,OAAP,CAAeX,KAAK,IAAI;AACpB,kBAAMG,KAAK,GAAG,KAAKC,aAAL,CAAmBC,WAAnB,CAA+BL,KAA/B,CAAd;AACA,kBAAMY,UAAU,GAAGlB,aAAa,CAACQ,aAAd,CAA4BF,KAA5B,CAAnB;AACAxB,cAAAA,OAAO,CAACC,GAAR,QAAiBuB,KAAjB,UAA2BG,KAA3B,yBAAyCS,UAAU,GAAG,KAAH,GAAW,KAA9D;AACH,aAJD;AAKH;AACJ;;AAjJ0C,O;;;;;iBAGZ,I", "sourcesContent": ["import { _decorator, Component, KeyCode, Input, input } from 'cc';\nimport { PlayerManager } from './PlayerManager';\nimport { SelectManager } from './SelectManager';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 车辆购买系统测试脚本\n * \n * 测试键位：\n * - 1: 添加500金币\n * - 2: 减少200金币\n * - 3: 尝试购买car-2 (500金币)\n * - 4: 尝试购买car-3 (1000金币)\n * - 5: 重置玩家数据\n * - 6: 显示当前状态\n */\n@ccclass('CarPurchaseTest')\nexport class CarPurchaseTest extends Component {\n    \n    @property(SelectManager)\n    selectManager: SelectManager = null!;\n\n    onLoad() {\n        // 启用键盘输入\n        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        console.log('=== 车辆购买系统测试 ===');\n        console.log('测试键位：');\n        console.log('1: 添加500金币');\n        console.log('2: 减少200金币');\n        console.log('3: 尝试购买car-2 (500金币)');\n        console.log('4: 尝试购买car-3 (1000金币)');\n        console.log('5: 重置玩家数据');\n        console.log('6: 显示当前状态');\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n    }\n\n    private onKeyDown(event: any) {\n        switch (event.keyCode) {\n            case KeyCode.DIGIT_1:\n                this.addMoney(500);\n                break;\n            case KeyCode.DIGIT_2:\n                this.reduceMoney(200);\n                break;\n            case KeyCode.DIGIT_3:\n                this.testPurchaseCar('car-2');\n                break;\n            case KeyCode.DIGIT_4:\n                this.testPurchaseCar('car-3');\n                break;\n            case KeyCode.DIGIT_5:\n                this.resetPlayerData();\n                break;\n            case KeyCode.DIGIT_6:\n                this.showCurrentStatus();\n                break;\n        }\n    }\n\n    /**\n     * 添加金币\n     */\n    private addMoney(amount: number) {\n        const playerManager = PlayerManager.instance;\n        if (playerManager) {\n            playerManager.addMoney(amount);\n            console.log(`添加 ${amount} 金币，当前金币: ${playerManager.playerData.money}`);\n\n            // PlayerInfoUI会自动更新金币显示，无需手动调用\n        }\n    }\n\n    /**\n     * 减少金币\n     */\n    private reduceMoney(amount: number) {\n        const playerManager = PlayerManager.instance;\n        if (playerManager) {\n            const success = playerManager.spendMoney(amount);\n            if (success) {\n                console.log(`消费 ${amount} 金币，当前金币: ${playerManager.playerData.money}`);\n            } else {\n                console.log(`金币不足，无法消费 ${amount} 金币，当前金币: ${playerManager.playerData.money}`);\n            }\n\n            // PlayerInfoUI会自动更新金币显示，无需手动调用\n        }\n    }\n\n    /**\n     * 测试购买车辆\n     */\n    private testPurchaseCar(carId: string) {\n        const playerManager = PlayerManager.instance;\n        if (!playerManager) {\n            console.error('PlayerManager 实例不存在');\n            return;\n        }\n\n        console.log(`=== 尝试购买车辆 ${carId} ===`);\n        \n        // 检查车辆是否已解锁\n        if (playerManager.isCarUnlocked(carId)) {\n            console.log(`车辆 ${carId} 已经解锁`);\n            return;\n        }\n\n        // 获取车辆价格\n        const price = this.selectManager ? this.selectManager.getCarPrice(carId) : 0;\n        console.log(`车辆价格: ${price} 金币`);\n        console.log(`当前金币: ${playerManager.playerData.money}`);\n\n        // 模拟购买\n        if (this.selectManager) {\n            this.selectManager.onPurchaseCar(carId);\n        }\n    }\n\n    /**\n     * 重置玩家数据\n     */\n    private resetPlayerData() {\n        const playerManager = PlayerManager.instance;\n        if (playerManager) {\n            playerManager.resetPlayerData();\n            console.log('玩家数据已重置');\n\n            // 更新SelectManager的显示\n            if (this.selectManager) {\n                this.selectManager.updateCarToggles();\n            }\n            // PlayerInfoUI会自动更新金币显示，无需手动调用\n        }\n    }\n\n    /**\n     * 显示当前状态\n     */\n    private showCurrentStatus() {\n        const playerManager = PlayerManager.instance;\n        if (!playerManager) {\n            console.error('PlayerManager 实例不存在');\n            return;\n        }\n\n        console.log('=== 当前状态 ===');\n        console.log(`金币: ${playerManager.playerData.money}`);\n        console.log(`已解锁车辆: ${playerManager.playerData.unlockedCars.join(', ')}`);\n        \n        // 显示所有车辆的价格和解锁状态\n        if (this.selectManager) {\n            const carIds = ['car-1', 'car-2', 'car-3', 'car-4', 'car-5'];\n            console.log('车辆状态:');\n            carIds.forEach(carId => {\n                const price = this.selectManager.getCarPrice(carId);\n                const isUnlocked = playerManager.isCarUnlocked(carId);\n                console.log(`  ${carId}: ${price} 金币 - ${isUnlocked ? '已解锁' : '未解锁'}`);\n            });\n        }\n    }\n}\n"]}