{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts"], "names": ["_decorator", "Component", "Label", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_player<PERSON><PERSON><PERSON>", "onLoad", "instance", "addDataChangeListener", "onPlayerDataChanged", "bind", "updateUI", "start", "bindButtonEvents", "isWeChatMiniGame", "wechatLoginBtn", "node", "active", "onDestroy", "removeDataChangeListener", "addMoneyBtn", "on", "EventType", "CLICK", "onAddMoneyClick", "addExpBtn", "onAddExpClick", "unlockCarBtn", "onUnlockCarClick", "unlockLevelBtn", "onUnlockLevelClick", "onWechatLoginClick", "data", "console", "log", "player<PERSON><PERSON>", "levelLabel", "string", "level", "moneyLabel", "money", "experienceLabel", "experience", "addMoney", "addExperience", "carId", "unlockCar", "levelId", "unlockLevel", "wechatLogin", "simulateRaceComplete", "raceTime", "stars", "updateLevelProgress", "updateStatistics", "totalRaces", "statistics", "totalWins", "totalDistance", "simulateCarUpgrade", "currentCar", "upgradeCost", "spendMoney", "parts", "randomPart", "Math", "floor", "random", "length", "upgradeCarPart", "exportPlayerData", "jsonData", "navigator", "clipboard", "writeText", "then", "resetPlayerData", "confirm"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AACpCC,MAAAA,a,iBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;sCAGjBO,oB,WADZF,OAAO,CAAC,sBAAD,C,UAEHC,QAAQ,CAACJ,KAAD,C,UAGRI,QAAQ,CAACJ,KAAD,C,UAGRI,QAAQ,CAACJ,KAAD,C,UAGRI,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,2BAvBb,MACaI,oBADb,SAC0CN,SAD1C,CACoD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAyBxCO,cAzBwC,GAyBR,IAzBQ;AAAA;;AA2BhDC,QAAAA,MAAM,GAAG;AACL;AACA,eAAKD,cAAL,GAAsB;AAAA;AAAA,8CAAcE,QAApC,CAFK,CAIL;;AACA,eAAKF,cAAL,CAAoBG,qBAApB,CAA0C,KAAKC,mBAAL,CAAyBC,IAAzB,CAA8B,IAA9B,CAA1C,EALK,CAOL;;;AACA,eAAKC,QAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,gBAAL,GAFI,CAIJ;;AACA,cAAI,KAAKR,cAAL,CAAoBS,gBAAxB,EAA0C;AACtC,iBAAKC,cAAL,CAAoBC,IAApB,CAAyBC,MAAzB,GAAkC,IAAlC;AACH,WAFD,MAEO;AACH,iBAAKF,cAAL,CAAoBC,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAKb,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBc,wBAApB,CAA6C,KAAKV,mBAAL,CAAyBC,IAAzB,CAA8B,IAA9B,CAA7C;AACH;AACJ;AAED;AACJ;AACA;;;AACYG,QAAAA,gBAAgB,GAAG;AACvB,cAAI,KAAKO,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBJ,IAAjB,CAAsBK,EAAtB,CAAyBrB,MAAM,CAACsB,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,eAAtD,EAAuE,IAAvE;AACH;;AAED,cAAI,KAAKC,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeT,IAAf,CAAoBK,EAApB,CAAuBrB,MAAM,CAACsB,SAAP,CAAiBC,KAAxC,EAA+C,KAAKG,aAApD,EAAmE,IAAnE;AACH;;AAED,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBX,IAAlB,CAAuBK,EAAvB,CAA0BrB,MAAM,CAACsB,SAAP,CAAiBC,KAA3C,EAAkD,KAAKK,gBAAvD,EAAyE,IAAzE;AACH;;AAED,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBb,IAApB,CAAyBK,EAAzB,CAA4BrB,MAAM,CAACsB,SAAP,CAAiBC,KAA7C,EAAoD,KAAKO,kBAAzD,EAA6E,IAA7E;AACH;;AAED,cAAI,KAAKf,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBC,IAApB,CAAyBK,EAAzB,CAA4BrB,MAAM,CAACsB,SAAP,CAAiBC,KAA7C,EAAoD,KAAKQ,kBAAzD,EAA6E,IAA7E;AACH;AACJ;AAED;AACJ;AACA;;;AACYtB,QAAAA,mBAAmB,CAACuB,IAAD,EAAmB;AAC1C,eAAKrB,QAAL;AACAsB,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBF,IAAxB;AACH;AAED;AACJ;AACA;;;AACYrB,QAAAA,QAAQ,GAAG;AACf,cAAI,CAAC,KAAKN,cAAV,EAA0B;AAE1B,cAAM2B,IAAI,GAAG,KAAK3B,cAAL,CAAoB8B,UAAjC;;AAEA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,sBAAgCL,IAAI,CAACM,KAArC;AACH;;AAED,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBF,MAAhB,sBAAgCL,IAAI,CAACQ,KAArC;AACH;;AAED,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBJ,MAArB,sBAAqCL,IAAI,CAACU,UAA1C,SAAwDV,IAAI,CAACM,KAAL,GAAa,GAArE;AACH;AACJ;AAED;AACJ;AACA;;;AACYd,QAAAA,eAAe,GAAG;AACtB,eAAKnB,cAAL,CAAoBsC,QAApB,CAA6B,GAA7B;;AACAV,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACH;AAED;AACJ;AACA;;;AACYR,QAAAA,aAAa,GAAG;AACpB,eAAKrB,cAAL,CAAoBuC,aAApB,CAAkC,EAAlC;;AACAX,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH;AAED;AACJ;AACA;;;AACYN,QAAAA,gBAAgB,GAAG;AACvB,cAAMiB,KAAK,GAAG,SAAd;;AACA,cAAI,KAAKxC,cAAL,CAAoByC,SAApB,CAA8BD,KAA9B,CAAJ,EAA0C;AACtCZ,YAAAA,OAAO,CAACC,GAAR,sCAAsBW,KAAtB;AACH,WAFD,MAEO;AACHZ,YAAAA,OAAO,CAACC,GAAR,mBAAkBW,KAAlB;AACH;AACJ;AAED;AACJ;AACA;;;AACYf,QAAAA,kBAAkB,GAAG;AACzB,cAAMiB,OAAO,GAAG,WAAhB;;AACA,cAAI,KAAK1C,cAAL,CAAoB2C,WAApB,CAAgCD,OAAhC,CAAJ,EAA8C;AAC1Cd,YAAAA,OAAO,CAACC,GAAR,sCAAsBa,OAAtB;AACH,WAFD,MAEO;AACHd,YAAAA,OAAO,CAACC,GAAR,mBAAkBa,OAAlB;AACH;AACJ;AAED;AACJ;AACA;;;AACkBhB,QAAAA,kBAAkB,GAAG;AAAA;;AAAA;AAC/B,sBAAU,KAAI,CAAC1B,cAAL,CAAoB4C,WAApB,EAAV,EAA6C;AACzChB,cAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AACH,aAFD,MAEO;AACHD,cAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AACH;AAL8B;AAMlC;AAED;AACJ;AACA;;;AACWgB,QAAAA,oBAAoB,GAAG;AAC1B;AACA,cAAMH,OAAO,GAAG,WAAhB;AACA,cAAMI,QAAQ,GAAG,KAAjB,CAH0B,CAGF;;AACxB,cAAMC,KAAK,GAAG,CAAd,CAJ0B,CAM1B;;AACA,eAAK/C,cAAL,CAAoBgD,mBAApB,CAAwCN,OAAxC,EAAiDI,QAAjD,EAA2DC,KAA3D,EAP0B,CAS1B;;;AACA,eAAK/C,cAAL,CAAoBsC,QAApB,CAA6B,GAA7B;;AACA,eAAKtC,cAAL,CAAoBuC,aAApB,CAAkC,GAAlC,EAX0B,CAa1B;;;AACA,eAAKvC,cAAL,CAAoBiD,gBAApB,CAAqC;AACjCC,YAAAA,UAAU,EAAE,KAAKlD,cAAL,CAAoB8B,UAApB,CAA+BqB,UAA/B,CAA0CD,UAA1C,GAAuD,CADlC;AAEjCE,YAAAA,SAAS,EAAE,KAAKpD,cAAL,CAAoB8B,UAApB,CAA+BqB,UAA/B,CAA0CC,SAA1C,GAAsD,CAFhC;AAGjCC,YAAAA,aAAa,EAAE,KAAKrD,cAAL,CAAoB8B,UAApB,CAA+BqB,UAA/B,CAA0CE,aAA1C,GAA0D;AAHxC,WAArC;;AAMAzB,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACH;AAED;AACJ;AACA;;;AACWyB,QAAAA,kBAAkB,GAAG;AACxB,cAAMd,KAAK,GAAG,KAAKxC,cAAL,CAAoB8B,UAApB,CAA+ByB,UAA7C;AACA,cAAMC,WAAW,GAAG,GAApB;;AAEA,cAAI,KAAKxD,cAAL,CAAoByD,UAApB,CAA+BD,WAA/B,CAAJ,EAAiD;AAC7C;AACA,gBAAME,KAAwD,GAAG,CAAC,QAAD,EAAW,OAAX,EAAoB,YAApB,EAAkC,OAAlC,CAAjE;AACA,gBAAMC,UAAU,GAAGD,KAAK,CAACE,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBJ,KAAK,CAACK,MAAjC,CAAD,CAAxB;;AAEA,gBAAI,KAAK/D,cAAL,CAAoBgE,cAApB,CAAmCxB,KAAnC,EAA0CmB,UAA1C,CAAJ,EAA2D;AACvD/B,cAAAA,OAAO,CAACC,GAAR,mBAAkBW,KAAlB,gBAA6BmB,UAA7B;AACH,aAFD,MAEO;AACH/B,cAAAA,OAAO,CAACC,GAAR,mBAAkBW,KAAlB,gBAA6BmB,UAA7B;AACH;AACJ,WAVD,MAUO;AACH/B,YAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACWoC,QAAAA,gBAAgB,GAAG;AACtB,cAAMC,QAAQ,GAAG,KAAKlE,cAAL,CAAoBiE,gBAApB,EAAjB;;AACArC,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuBqC,QAAvB,EAFsB,CAItB;;AACA,cAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAAlD,EAA6D;AACzDD,YAAAA,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8BH,QAA9B,EAAwCI,IAAxC,CAA6C,MAAM;AAC/C1C,cAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ;AACH,aAFD;AAGH;AACJ;AAED;AACJ;AACA;;;AACW0C,QAAAA,eAAe,GAAG;AACrB,cAAIC,OAAO,CAAC,uBAAD,CAAX,EAAsC;AAClC,iBAAKxE,cAAL,CAAoBuE,eAApB;;AACA3C,YAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH;AACJ;;AAzO+C,O;;;;;iBAE5B,I;;;;;;;iBAGA,I;;;;;;;iBAGK,I;;;;;;;iBAGH,I;;;;;;;iBAGF,I;;;;;;;iBAGG,I;;;;;;;iBAGE,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Node, Label, Button } from 'cc';\nimport { PlayerManager, PlayerData } from './PlayerManager';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlayerManagerExample')\nexport class PlayerManagerExample extends Component {\n    @property(Label)\n    levelLabel: Label = null!;\n    \n    @property(Label)\n    moneyLabel: Label = null!;\n    \n    @property(Label)\n    experienceLabel: Label = null!;\n    \n    @property(Button)\n    addMoneyBtn: Button = null!;\n    \n    @property(Button)\n    addExpBtn: Button = null!;\n    \n    @property(Button)\n    unlockCarBtn: Button = null!;\n    \n    @property(Button)\n    unlockLevelBtn: Button = null!;\n    \n    @property(Button)\n    wechatLoginBtn: Button = null!;\n    \n    private _playerManager: PlayerManager = null!;\n    \n    onLoad() {\n        // 获取PlayerManager实例\n        this._playerManager = PlayerManager.instance;\n        \n        // 添加数据变化监听\n        this._playerManager.addDataChangeListener(this.onPlayerDataChanged.bind(this));\n        \n        // 更新UI\n        this.updateUI();\n    }\n    \n    start() {\n        // 绑定按钮事件\n        this.bindButtonEvents();\n        \n        // 检查微信环境并显示登录按钮\n        if (this._playerManager.isWeChatMiniGame) {\n            this.wechatLoginBtn.node.active = true;\n        } else {\n            this.wechatLoginBtn.node.active = false;\n        }\n    }\n    \n    onDestroy() {\n        // 移除数据变化监听\n        if (this._playerManager) {\n            this._playerManager.removeDataChangeListener(this.onPlayerDataChanged.bind(this));\n        }\n    }\n    \n    /**\n     * 绑定按钮事件\n     */\n    private bindButtonEvents() {\n        if (this.addMoneyBtn) {\n            this.addMoneyBtn.node.on(Button.EventType.CLICK, this.onAddMoneyClick, this);\n        }\n        \n        if (this.addExpBtn) {\n            this.addExpBtn.node.on(Button.EventType.CLICK, this.onAddExpClick, this);\n        }\n        \n        if (this.unlockCarBtn) {\n            this.unlockCarBtn.node.on(Button.EventType.CLICK, this.onUnlockCarClick, this);\n        }\n        \n        if (this.unlockLevelBtn) {\n            this.unlockLevelBtn.node.on(Button.EventType.CLICK, this.onUnlockLevelClick, this);\n        }\n        \n        if (this.wechatLoginBtn) {\n            this.wechatLoginBtn.node.on(Button.EventType.CLICK, this.onWechatLoginClick, this);\n        }\n    }\n    \n    /**\n     * 玩家数据变化回调\n     */\n    private onPlayerDataChanged(data: PlayerData) {\n        this.updateUI();\n        console.log('玩家数据已更新:', data);\n    }\n    \n    /**\n     * 更新UI显示\n     */\n    private updateUI() {\n        if (!this._playerManager) return;\n        \n        const data = this._playerManager.playerData;\n        \n        if (this.levelLabel) {\n            this.levelLabel.string = `等级: ${data.level}`;\n        }\n        \n        if (this.moneyLabel) {\n            this.moneyLabel.string = `金钱: ${data.money}`;\n        }\n        \n        if (this.experienceLabel) {\n            this.experienceLabel.string = `经验: ${data.experience}/${data.level * 100}`;\n        }\n    }\n    \n    /**\n     * 增加金钱按钮点击\n     */\n    private onAddMoneyClick() {\n        this._playerManager.addMoney(100);\n        console.log('增加了100金钱');\n    }\n    \n    /**\n     * 增加经验按钮点击\n     */\n    private onAddExpClick() {\n        this._playerManager.addExperience(50);\n        console.log('增加了50经验');\n    }\n    \n    /**\n     * 解锁车辆按钮点击\n     */\n    private onUnlockCarClick() {\n        const carId = 'car_002';\n        if (this._playerManager.unlockCar(carId)) {\n            console.log(`解锁了车辆: ${carId}`);\n        } else {\n            console.log(`车辆 ${carId} 已经解锁`);\n        }\n    }\n    \n    /**\n     * 解锁关卡按钮点击\n     */\n    private onUnlockLevelClick() {\n        const levelId = 'level_002';\n        if (this._playerManager.unlockLevel(levelId)) {\n            console.log(`解锁了关卡: ${levelId}`);\n        } else {\n            console.log(`关卡 ${levelId} 已经解锁`);\n        }\n    }\n    \n    /**\n     * 微信登录按钮点击\n     */\n    private async onWechatLoginClick() {\n        if (await this._playerManager.wechatLogin()) {\n            console.log('微信登录成功');\n        } else {\n            console.log('微信登录失败');\n        }\n    }\n    \n    /**\n     * 模拟比赛完成\n     */\n    public simulateRaceComplete() {\n        // 模拟比赛完成后的数据更新\n        const levelId = 'level_001';\n        const raceTime = 45000; // 45秒\n        const stars = 2;\n        \n        // 更新关卡进度\n        this._playerManager.updateLevelProgress(levelId, raceTime, stars);\n        \n        // 增加金钱和经验\n        this._playerManager.addMoney(200);\n        this._playerManager.addExperience(100);\n        \n        // 更新统计数据\n        this._playerManager.updateStatistics({\n            totalRaces: this._playerManager.playerData.statistics.totalRaces + 1,\n            totalWins: this._playerManager.playerData.statistics.totalWins + 1,\n            totalDistance: this._playerManager.playerData.statistics.totalDistance + 5000\n        });\n        \n        console.log('比赛完成，数据已更新');\n    }\n    \n    /**\n     * 模拟车辆升级\n     */\n    public simulateCarUpgrade() {\n        const carId = this._playerManager.playerData.currentCar;\n        const upgradeCost = 500;\n        \n        if (this._playerManager.spendMoney(upgradeCost)) {\n            // 随机升级一个部件\n            const parts: Array<keyof import('./PlayerManager').CarUpgrade> = ['engine', 'tires', 'suspension', 'nitro'];\n            const randomPart = parts[Math.floor(Math.random() * parts.length)];\n            \n            if (this._playerManager.upgradeCarPart(carId, randomPart)) {\n                console.log(`车辆 ${carId} 的 ${randomPart} 升级成功`);\n            } else {\n                console.log(`车辆 ${carId} 的 ${randomPart} 已达到最高等级`);\n            }\n        } else {\n            console.log('金钱不足，无法升级');\n        }\n    }\n    \n    /**\n     * 导出玩家数据（用于调试）\n     */\n    public exportPlayerData() {\n        const jsonData = this._playerManager.exportPlayerData();\n        console.log('玩家数据导出:', jsonData);\n        \n        // 在实际项目中，可以将数据复制到剪贴板或保存到文件\n        if (typeof navigator !== 'undefined' && navigator.clipboard) {\n            navigator.clipboard.writeText(jsonData).then(() => {\n                console.log('玩家数据已复制到剪贴板');\n            });\n        }\n    }\n    \n    /**\n     * 重置玩家数据（用于调试）\n     */\n    public resetPlayerData() {\n        if (confirm('确定要重置所有玩家数据吗？此操作不可恢复！')) {\n            this._playerManager.resetPlayerData();\n            console.log('玩家数据已重置');\n        }\n    }\n} "]}