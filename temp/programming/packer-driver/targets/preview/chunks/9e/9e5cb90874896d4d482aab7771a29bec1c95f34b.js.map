{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "GameManager", "ccclass", "property", "PausePanel", "start", "bindButtonEvents", "resumeButton", "node", "on", "EventType", "CLICK", "onResumeClick", "restartButton", "onRestartClick", "mainMenuButton", "onMainMenuClick", "gameManager", "getInstance", "resumeGame", "restartGame", "returnToMainMenu", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;;AAC7BC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;4BAGjBM,U,WADZF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,2BARb,MACaI,UADb,SACgCL,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAER;AAFQ;;AAKP;AALO;AAAA;;AAQN;AAEhCM,QAAAA,KAAK,GAAG;AACJ,eAAKC,gBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,gBAAgB,GAAG;AACvB,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBC,IAAlB,CAAuBC,EAAvB,CAA0BT,MAAM,CAACU,SAAP,CAAiBC,KAA3C,EAAkD,KAAKC,aAAvD,EAAsE,IAAtE;AACH;;AAED,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBL,IAAnB,CAAwBC,EAAxB,CAA2BT,MAAM,CAACU,SAAP,CAAiBC,KAA5C,EAAmD,KAAKG,cAAxD,EAAwE,IAAxE;AACH;;AAED,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBP,IAApB,CAAyBC,EAAzB,CAA4BT,MAAM,CAACU,SAAP,CAAiBC,KAA7C,EAAoD,KAAKK,eAAzD,EAA0E,IAA1E;AACH;AACJ;AAED;AACJ;AACA;;;AACYJ,QAAAA,aAAa,GAAG;AACpB,cAAMK,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACE,UAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYL,QAAAA,cAAc,GAAG;AACrB,cAAMG,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACG,WAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYJ,QAAAA,eAAe,GAAG;AACtB,cAAMC,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACI,gBAAZ;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAKf,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBC,IAAlB,CAAuBe,GAAvB,CAA2BvB,MAAM,CAACU,SAAP,CAAiBC,KAA5C,EAAmD,KAAKC,aAAxD,EAAuE,IAAvE;AACH;;AACD,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBL,IAAnB,CAAwBe,GAAxB,CAA4BvB,MAAM,CAACU,SAAP,CAAiBC,KAA7C,EAAoD,KAAKG,cAAzD,EAAyE,IAAzE;AACH;;AACD,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBP,IAApB,CAAyBe,GAAzB,CAA6BvB,MAAM,CAACU,SAAP,CAAiBC,KAA9C,EAAqD,KAAKK,eAA1D,EAA2E,IAA3E;AACH;AACJ;;AAxEqC,O;;;;;iBAEf,I;;;;;;;iBAGC,I;;;;;;;iBAGC,I", "sourcesContent": ["import { _decorator, Component, Node, Button } from 'cc';\nimport { GameManager } from './GameManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('PausePanel')\nexport class PausePanel extends Component {\n    @property(Button)\n    resumeButton: Button = null!; // 继续游戏按钮\n\n    @property(Button)\n    restartButton: Button = null!; // 重新开始按钮\n\n    @property(Button)\n    mainMenuButton: Button = null!; // 返回主菜单按钮\n\n    start() {\n        this.bindButtonEvents();\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    private bindButtonEvents() {\n        if (this.resumeButton) {\n            this.resumeButton.node.on(Button.EventType.CLICK, this.onResumeClick, this);\n        }\n\n        if (this.restartButton) {\n            this.restartButton.node.on(Button.EventType.CLICK, this.onRestartClick, this);\n        }\n\n        if (this.mainMenuButton) {\n            this.mainMenuButton.node.on(Button.EventType.CLICK, this.onMainMenuClick, this);\n        }\n    }\n\n    /**\n     * 继续游戏按钮点击\n     */\n    private onResumeClick() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.resumeGame();\n        }\n    }\n\n    /**\n     * 重新开始按钮点击\n     */\n    private onRestartClick() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.restartGame();\n        }\n    }\n\n    /**\n     * 返回主菜单按钮点击\n     */\n    private onMainMenuClick() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.returnToMainMenu();\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        if (this.resumeButton) {\n            this.resumeButton.node.off(Button.EventType.CLICK, this.onResumeClick, this);\n        }\n        if (this.restartButton) {\n            this.restartButton.node.off(Button.EventType.CLICK, this.onRestartClick, this);\n        }\n        if (this.mainMenuButton) {\n            this.mainMenuButton.node.off(Button.EventType.CLICK, this.onMainMenuClick, this);\n        }\n    }\n}\n"]}