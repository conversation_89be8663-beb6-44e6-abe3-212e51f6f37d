{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts"], "names": ["_decorator", "Component", "Node", "director", "UIOpacity", "tween", "view", "UITransform", "color", "Graphics", "<PERSON><PERSON>", "ccclass", "SceneFader", "uiOpacity", "faderNode", "loadScene", "scene<PERSON><PERSON>", "instance", "onLoad", "node", "destroy", "addPersistRootNode", "getComponent", "addComponent", "createFaderNode", "fadeIn", "transform", "size", "getVisibleSize", "setContentSize", "width", "height", "opacity", "<PERSON><PERSON><PERSON><PERSON>", "graphics", "fillColor", "fillRect", "duration", "to", "start", "fadeOut", "onComplete", "call", "onDestroy"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;;;;;;;;;OAChG;AAAEC,QAAAA;AAAF,O,GAAcX,U;;4BAGPY,U,WADZD,OAAO,CAAC,YAAD,C,2BAAR,MACaC,UADb,SACgCX,SADhC,CAC0C;AAAA;AAAA;AAAA,eAE9BY,SAF8B,GAEA,IAFA;AAAA,eAG9BC,SAH8B,GAGL,IAHK;AAAA;;AAKtC;AACJ;AACA;AACoB,eAATC,SAAS,CAACC,SAAD,EAAoB;AAChC,cAAIJ,UAAU,CAACK,QAAf,EAAyB;AACrBL,YAAAA,UAAU,CAACK,QAAX,CAAoBF,SAApB,CAA8BC,SAA9B;AACH,WAFD,MAEO;AACH;AACAb,YAAAA,QAAQ,CAACY,SAAT,CAAmBC,SAAnB;AACH;AACJ;;AAEDE,QAAAA,MAAM,GAAG;AACL;AACA,cAAIN,UAAU,CAACK,QAAf,EAAyB;AACrB,iBAAKE,IAAL,CAAUC,OAAV;AACA;AACH;;AAEDR,UAAAA,UAAU,CAACK,QAAX,GAAsB,IAAtB,CAPK,CASL;;AACAd,UAAAA,QAAQ,CAACkB,kBAAT,CAA4B,KAAKF,IAAjC,EAVK,CAYL;;AACA,cAAI,CAAC,KAAKA,IAAL,CAAUG,YAAV,CAAuBZ,MAAvB,CAAL,EAAqC;AACjC,iBAAKS,IAAL,CAAUI,YAAV,CAAuBb,MAAvB;AACH;;AAED,eAAKc,eAAL;AACA,eAAKC,MAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,eAAe,GAAG;AACtB;AACA,eAAKV,SAAL,GAAiB,IAAIZ,IAAJ,CAAS,OAAT,CAAjB,CAFsB,CAItB;;AACA,cAAMwB,SAAS,GAAG,KAAKZ,SAAL,CAAeS,YAAf,CAA4BhB,WAA5B,CAAlB;AACA,cAAMoB,IAAI,GAAGrB,IAAI,CAACsB,cAAL,EAAb;AACAF,UAAAA,SAAS,CAACG,cAAV,CAAyBF,IAAI,CAACG,KAA9B,EAAqCH,IAAI,CAACI,MAA1C,EAPsB,CAStB;;AACA,eAAKlB,SAAL,GAAiB,KAAKC,SAAL,CAAeS,YAAf,CAA4BnB,SAA5B,CAAjB;AACA,eAAKS,SAAL,CAAemB,OAAf,GAAyB,CAAzB,CAXsB,CAatB;;AACA,eAAKb,IAAL,CAAUc,QAAV,CAAmB,KAAKnB,SAAxB,EAdsB,CAgBtB;;AACA,cAAMoB,QAAQ,GAAG,KAAKpB,SAAL,CAAeS,YAAf,CAA4Bd,QAA5B,CAAjB;AACAyB,UAAAA,QAAQ,CAACC,SAAT,GAAqB3B,KAAK,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,GAAV,CAA1B;AACA0B,UAAAA,QAAQ,CAACE,QAAT,CAAkB,CAACT,IAAI,CAACG,KAAN,GAAc,CAAhC,EAAmC,CAACH,IAAI,CAACI,MAAN,GAAe,CAAlD,EAAqDJ,IAAI,CAACG,KAA1D,EAAiEH,IAAI,CAACI,MAAtE;AACH;AAED;AACJ;AACA;;;AACIN,QAAAA,MAAM,CAACY,QAAD,EAAyB;AAAA,cAAxBA,QAAwB;AAAxBA,YAAAA,QAAwB,GAAL,GAAK;AAAA;;AAC3B,cAAI,CAAC,KAAKxB,SAAV,EAAqB;AAErB,eAAKA,SAAL,CAAemB,OAAf,GAAyB,GAAzB;AACA3B,UAAAA,KAAK,CAAC,KAAKQ,SAAN,CAAL,CACKyB,EADL,CACQD,QADR,EACkB;AAAEL,YAAAA,OAAO,EAAE;AAAX,WADlB,EAEKO,KAFL;AAGH;AAED;AACJ;AACA;;;AACIC,QAAAA,OAAO,CAACC,UAAD,EAA0BJ,QAA1B,EAAkD;AAAA,cAAxBA,QAAwB;AAAxBA,YAAAA,QAAwB,GAAL,GAAK;AAAA;;AACrD,cAAI,CAAC,KAAKxB,SAAV,EAAqB;AACjB,gBAAI4B,UAAJ,EAAgBA,UAAU;AAC1B;AACH;;AAED,eAAK5B,SAAL,CAAemB,OAAf,GAAyB,CAAzB;AACA3B,UAAAA,KAAK,CAAC,KAAKQ,SAAN,CAAL,CACKyB,EADL,CACQD,QADR,EACkB;AAAEL,YAAAA,OAAO,EAAE;AAAX,WADlB,EAEKU,IAFL,CAEU,MAAM;AACR,gBAAID,UAAJ,EAAgBA,UAAU;AAC7B,WAJL,EAKKF,KALL;AAMH;AAED;AACJ;AACA;;;AACIxB,QAAAA,SAAS,CAACC,SAAD,EAAoB;AACzB,eAAKwB,OAAL,CAAa,MAAM;AACfrC,YAAAA,QAAQ,CAACY,SAAT,CAAmBC,SAAnB,EAA8B,MAAM;AAChC,mBAAKS,MAAL;AACH,aAFD;AAGH,WAJD;AAKH;;AAEDkB,QAAAA,SAAS,GAAG;AACR,cAAI/B,UAAU,CAACK,QAAX,KAAwB,IAA5B,EAAkC;AAC9BL,YAAAA,UAAU,CAACK,QAAX,GAAsB,IAAtB;AACH;AACJ;;AA5GqC,O,UACvBA,Q,GAA8B,I", "sourcesContent": ["import { _decorator, Component, Node, director, UIOpacity, tween, view, UITransform, color, Graphics, Canvas } from 'cc';\nconst { ccclass } = _decorator;\n\n@ccclass('SceneFader')\nexport class SceneFader extends Component {\n    private static instance: SceneFader | null = null;\n    private uiOpacity: UIOpacity | null = null;\n    private faderNode: Node | null = null;\n\n    /**\n     * 静态方法：使用渐变效果加载场景\n     */\n    static loadScene(sceneName: string) {\n        if (SceneFader.instance) {\n            SceneFader.instance.loadScene(sceneName);\n        } else {\n            // 如果没有实例，直接加载场景\n            director.loadScene(sceneName);\n        }\n    }\n\n    onLoad() {\n        // 确保只有一个实例\n        if (SceneFader.instance) {\n            this.node.destroy();\n            return;\n        }\n\n        SceneFader.instance = this;\n        \n        // 设置为常驻节点，不会在场景切换时被销毁\n        director.addPersistRootNode(this.node);\n        \n        // 添加Canvas组件\n        if (!this.node.getComponent(Canvas)) {\n            this.node.addComponent(Canvas);\n        }\n        \n        this.createFaderNode();\n        this.fadeIn();\n    }\n\n    /**\n     * 创建遮罩节点\n     */\n    private createFaderNode() {\n        // 创建遮罩节点\n        this.faderNode = new Node('Fader');\n        \n        // 设置UI变换组件\n        const transform = this.faderNode.addComponent(UITransform);\n        const size = view.getVisibleSize();\n        transform.setContentSize(size.width, size.height);\n        \n        // 添加透明度组件\n        this.uiOpacity = this.faderNode.addComponent(UIOpacity);\n        this.uiOpacity.opacity = 0;\n        \n        // 添加到当前节点\n        this.node.addChild(this.faderNode);\n        \n        // 创建黑色遮罩\n        const graphics = this.faderNode.addComponent(Graphics);\n        graphics.fillColor = color(0, 0, 0, 255);\n        graphics.fillRect(-size.width / 2, -size.height / 2, size.width, size.height);\n    }\n\n    /**\n     * 渐入效果（从黑屏到透明）\n     */\n    fadeIn(duration: number = 0.5) {\n        if (!this.uiOpacity) return;\n        \n        this.uiOpacity.opacity = 255;\n        tween(this.uiOpacity)\n            .to(duration, { opacity: 0 })\n            .start();\n    }\n\n    /**\n     * 渐出效果（从透明到黑屏）\n     */\n    fadeOut(onComplete?: () => void, duration: number = 0.5) {\n        if (!this.uiOpacity) {\n            if (onComplete) onComplete();\n            return;\n        }\n        \n        this.uiOpacity.opacity = 0;\n        tween(this.uiOpacity)\n            .to(duration, { opacity: 255 })\n            .call(() => {\n                if (onComplete) onComplete();\n            })\n            .start();\n    }\n\n    /**\n     * 带渐变效果的场景加载\n     */\n    loadScene(sceneName: string) {\n        this.fadeOut(() => {\n            director.loadScene(sceneName, () => {\n                this.fadeIn();\n            });\n        });\n    }\n\n    onDestroy() {\n        if (SceneFader.instance === this) {\n            SceneFader.instance = null;\n        }\n    }\n}\n"]}