{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"], "names": ["_decorator", "Component", "sys", "director", "ccclass", "property", "LevelGrade", "<PERSON><PERSON><PERSON><PERSON>", "STORAGE_KEY", "STORAGE_KEY_BACKUP", "_playerData", "_wechatAPI", "_isWeChatMiniGame", "_userId", "_autoSaveInterval", "_lastAutoSaveTime", "_dataChangeCallbacks", "instance", "_instance", "player<PERSON><PERSON>", "isWeChatMiniGame", "userId", "onLoad", "node", "destroy", "addPersistRootNode", "_detectEnvironment", "resetPlayerData", "_initPlayerData", "loadPlayerData", "onDestroy", "update", "deltaTime", "currentTime", "Date", "now", "wx", "setStorageSync", "console", "log", "level", "money", "experience", "unlockedCars", "currentCar", "carUpgrades", "engine", "tires", "suspension", "nitro", "unlockedLevels", "currentLevel", "levelProgress", "stars", "completed", "bestTime", "grade", "F", "attempts", "settings", "soundVolume", "musicVolume", "statistics", "totalRaces", "totalWins", "totalMoneyEarned", "lastSaveTime", "createTime", "data", "getStorageSync", "error", "warn", "jsonData", "localStorage", "getItem", "JSON", "parse", "_mergePlayerData", "_notifyDataChange", "savePlayerData", "_saveToWeChatCloud", "setItem", "stringify", "defaultData", "savedData", "merged", "key", "hasOwnProperty", "Array", "isArray", "cloudCallFunction", "wechatLogin", "loginResult", "login", "code", "_loadFromWeChatCloud", "result", "addDataChangeListener", "callback", "push", "removeDataChangeListener", "index", "indexOf", "splice", "for<PERSON>ach", "addMoney", "amount", "spendMoney", "addExperience", "exp", "expNeeded", "unlockCar", "carId", "isCarUnlocked", "setCurrentCar", "upgradeCarPart", "part", "upgrade", "unlockLevel", "levelId", "updateLevelProgress", "time", "calculateLevelGrade", "progress", "checkAndUnlockNextLevel", "S", "A", "B", "C", "D", "currentLevelId", "currentProgress", "isGradePassable", "nextLevelId", "getNextLevelId", "match", "currentNumber", "parseInt", "getLevelProgress", "isLevelUnlocked", "getLevelGradeText", "getLevelGradeColor", "updateSettings", "updateStatistics", "updates", "exportPlayerData", "importPlayerData"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,G,OAAAA,G;AAAWC,MAAAA,Q,OAAAA,Q;;;;;;;;;OAE3C;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U,GAE9B;AAGA;AA4BA;AAQA;;AASA;4BACYM,U,0BAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;eAAAA,U;cASZ;AAMA;AAOA;;;+BAWaC,a,WADZH,OAAO,CAAC,eAAD,C,2BAAR,MACaG,aADb,SACmCN,SADnC,CAC6C;AAAA;AAAA;AAGzC;AAHyC,eAIxBO,WAJwB,GAIV,sBAJU;AAAA,eAKxBC,kBALwB,GAKH,6BALG;AAOzC;AAPyC,eAQjCC,WARiC,GAQP,IARO;AAUzC;AAVyC,eAWjCC,UAXiC,GAWF,IAXE;AAAA,eAYjCC,iBAZiC,GAYb,KAZa;AAAA,eAajCC,OAbiC,GAaf,EAbe;AAezC;AAfyC,eAgBjCC,iBAhBiC,GAgBL,KAhBK;AAgBE;AAhBF,eAiBjCC,iBAjBiC,GAiBL,CAjBK;AAmBzC;AAnByC,eAoBjCC,oBApBiC,GAoBsB,EApBtB;AAAA;;AAsBf,mBAARC,QAAQ,GAAkB;AACxC,iBAAOV,aAAa,CAACW,SAArB;AACH;;AAEoB,YAAVC,UAAU,GAAe;AAChC,iBAAO,KAAKT,WAAZ;AACH;;AAE0B,YAAhBU,gBAAgB,GAAY;AACnC,iBAAO,KAAKR,iBAAZ;AACH;;AAEgB,YAANS,MAAM,GAAW;AACxB,iBAAO,KAAKR,OAAZ;AACH;;AAEDS,QAAAA,MAAM,GAAG;AACL;AACA,cAAIf,aAAa,CAACW,SAAlB,EAA6B;AACzB,iBAAKK,IAAL,CAAUC,OAAV;AACA;AACH;;AAEDjB,UAAAA,aAAa,CAACW,SAAd,GAA0B,IAA1B,CAPK,CASL;AACA;;AACAf,UAAAA,QAAQ,CAACsB,kBAAT,CAA4B,KAAKF,IAAjC,EAXK,CAaL;;AACA,eAAKG,kBAAL;;AACA,eAAKC,eAAL,GAfK,CAiBL;;AACA,eAAKC,eAAL,GAlBK,CAoBL;;;AACA,eAAKC,cAAL;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAIvB,aAAa,CAACW,SAAd,KAA4B,IAAhC,EAAsC;AAClCX,YAAAA,aAAa,CAACW,SAAd,GAA0B,IAA1B;AACH;AACJ;;AAEDa,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB;AACA,cAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;;AACA,cAAIF,WAAW,GAAG,KAAKlB,iBAAnB,GAAuC,KAAKD,iBAAhD,EAAmE;AAC/D;AACA,iBAAKC,iBAAL,GAAyBkB,WAAzB;AACH;AACJ;AAED;AACJ;AACA;;;AACYP,QAAAA,kBAAkB,GAAG;AACzB;AACA,cAAI,OAAOU,EAAP,KAAc,WAAd,IAA6BA,EAAE,CAACC,cAApC,EAAoD;AAChD,iBAAKzB,iBAAL,GAAyB,IAAzB;AACA,iBAAKD,UAAL,GAAkByB,EAAlB;AACAE,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACH,WAJD,MAIO;AACH,iBAAK3B,iBAAL,GAAyB,KAAzB;AACA0B,YAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYX,QAAAA,eAAe,GAAG;AACtB,eAAKlB,WAAL,GAAmB;AACf8B,YAAAA,KAAK,EAAE,CADQ;AAEfC,YAAAA,KAAK,EAAE,KAFQ;AAGfC,YAAAA,UAAU,EAAE,CAHG;AAKfC,YAAAA,YAAY,EAAE,CAAC,OAAD,CALC;AAKU;AACzBC,YAAAA,UAAU,EAAE,OANG;AAOfC,YAAAA,WAAW,EAAE;AACT,uBAAS;AACLC,gBAAAA,MAAM,EAAE,CADH;AAELC,gBAAAA,KAAK,EAAE,CAFF;AAGLC,gBAAAA,UAAU,EAAE,CAHP;AAILC,gBAAAA,KAAK,EAAE;AAJF;AADA,aAPE;AAgBfC,YAAAA,cAAc,EAAE,CAAC,SAAD,CAhBD;AAgBc;AAC7BC,YAAAA,YAAY,EAAE,SAjBC;AAkBfC,YAAAA,aAAa,EAAE;AACX,yBAAW;AACPC,gBAAAA,KAAK,EAAE,CADA;AAEPC,gBAAAA,SAAS,EAAE,KAFJ;AAGPC,gBAAAA,QAAQ,EAAE,CAHH;AAIPC,gBAAAA,KAAK,EAAElD,UAAU,CAACmD,CAJX;AAKPC,gBAAAA,QAAQ,EAAE;AALH;AADA,aAlBA;AA4BfC,YAAAA,QAAQ,EAAE;AACNC,cAAAA,WAAW,EAAE,GADP;AAENC,cAAAA,WAAW,EAAE;AAFP,aA5BK;AAiCfC,YAAAA,UAAU,EAAE;AACRC,cAAAA,UAAU,EAAE,CADJ;AAERC,cAAAA,SAAS,EAAE,CAFH;AAGRC,cAAAA,gBAAgB,EAAE;AAHV,aAjCG;AAuCfC,YAAAA,YAAY,EAAEhC,IAAI,CAACC,GAAL,EAvCC;AAwCfgC,YAAAA,UAAU,EAAEjC,IAAI,CAACC,GAAL;AAxCG,WAAnB;AA0CH;AAED;AACJ;AACA;;;AACiBN,QAAAA,cAAc,GAAkB;AAAA;;AAAA;AACzC,gBAAI;AACA,kBAAIuC,KAAS,GAAG,IAAhB;;AAEA,kBAAI,KAAI,CAACxD,iBAAL,IAA0B,KAAI,CAACD,UAAnC,EAA+C;AAC3C;AACA,oBAAI;AACAyD,kBAAAA,KAAI,GAAG,KAAI,CAACzD,UAAL,CAAgB0D,cAAhB,CAA+B,KAAI,CAAC7D,WAApC,CAAP;AACH,iBAFD,CAEE,OAAO8D,KAAP,EAAc;AACZhC,kBAAAA,OAAO,CAACiC,IAAR,CAAa,kBAAb,EAAiCD,KAAjC;AACAF,kBAAAA,KAAI,GAAG,KAAI,CAACzD,UAAL,CAAgB0D,cAAhB,CAA+B,KAAI,CAAC5D,kBAApC,CAAP;AACH;AACJ,eARD,MAQO;AACH;AACA,oBAAM+D,QAAQ,GAAGtE,GAAG,CAACuE,YAAJ,CAAiBC,OAAjB,CAAyB,KAAI,CAAClE,WAA9B,CAAjB;;AACA,oBAAIgE,QAAJ,EAAc;AACVJ,kBAAAA,KAAI,GAAGO,IAAI,CAACC,KAAL,CAAWJ,QAAX,CAAP;AACH;AACJ;;AAED,kBAAIJ,KAAJ,EAAU;AACN;AACA,gBAAA,KAAI,CAAC1D,WAAL,GAAmB,KAAI,CAACmE,gBAAL,CAAsB,KAAI,CAACnE,WAA3B,EAAwC0D,KAAxC,CAAnB;AACA9B,gBAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACH,eAJD,MAIO;AACHD,gBAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACH,eAzBD,CA2BA;;;AACA,cAAA,KAAI,CAACuC,iBAAL;AAEH,aA9BD,CA8BE,OAAOR,KAAP,EAAc;AACZhC,cAAAA,OAAO,CAACgC,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACH;AAjCwC;AAkC5C;AAED;AACJ;AACA;;;AACiBS,QAAAA,cAAc,GAAkB;AAAA;;AAAA;AACzC,gBAAI;AACA,cAAA,MAAI,CAACrE,WAAL,CAAiBwD,YAAjB,GAAgChC,IAAI,CAACC,GAAL,EAAhC;;AAEA,kBAAI,MAAI,CAACvB,iBAAL,IAA0B,MAAI,CAACD,UAAnC,EAA+C;AAC3C;AACA,oBAAI;AACA,kBAAA,MAAI,CAACA,UAAL,CAAgB0B,cAAhB,CAA+B,MAAI,CAAC7B,WAApC,EAAiD,MAAI,CAACE,WAAtD,EADA,CAEA;;;AACA,kBAAA,MAAI,CAACC,UAAL,CAAgB0B,cAAhB,CAA+B,MAAI,CAAC5B,kBAApC,EAAwD,MAAI,CAACC,WAA7D;AACH,iBAJD,CAIE,OAAO4D,KAAP,EAAc;AACZhC,kBAAAA,OAAO,CAACgC,KAAR,CAAc,WAAd,EAA2BA,KAA3B,EADY,CAEZ;;AACA,wBAAM,MAAI,CAACU,kBAAL,EAAN;AACH;AACJ,eAXD,MAWO;AACH;AACA9E,gBAAAA,GAAG,CAACuE,YAAJ,CAAiBQ,OAAjB,CAAyB,MAAI,CAACzE,WAA9B,EAA2CmE,IAAI,CAACO,SAAL,CAAe,MAAI,CAACxE,WAApB,CAA3C;AACH;;AAED4B,cAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AAEH,aArBD,CAqBE,OAAO+B,KAAP,EAAc;AACZhC,cAAAA,OAAO,CAACgC,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACH;AAxBwC;AAyB5C;AAED;AACJ;AACA;;;AACYO,QAAAA,gBAAgB,CAACM,WAAD,EAA0BC,SAA1B,EAAsD;AAC1E,cAAMC,MAAM,gBAAQF,WAAR,CAAZ,CAD0E,CAG1E;;;AACA,eAAK,IAAMG,IAAX,IAAkBF,SAAlB,EAA6B;AACzB,gBAAIA,SAAS,CAACG,cAAV,CAAyBD,IAAzB,CAAJ,EAAmC;AAC/B,kBAAI,OAAOF,SAAS,CAACE,IAAD,CAAhB,KAA0B,QAA1B,IAAsCF,SAAS,CAACE,IAAD,CAAT,KAAmB,IAAzD,IAAiE,CAACE,KAAK,CAACC,OAAN,CAAcL,SAAS,CAACE,IAAD,CAAvB,CAAtE,EAAqG;AACjGD,gBAAAA,MAAM,CAACC,IAAD,CAAN,gBAAmBD,MAAM,CAACC,IAAD,CAAzB,EAAmCF,SAAS,CAACE,IAAD,CAA5C;AACH,eAFD,MAEO;AACHD,gBAAAA,MAAM,CAACC,IAAD,CAAN,GAAcF,SAAS,CAACE,IAAD,CAAvB;AACH;AACJ;AACJ;;AAED,iBAAOD,MAAP;AACH;AAED;AACJ;AACA;;;AACkBL,QAAAA,kBAAkB,GAAkB;AAAA;;AAAA;AAC9C,gBAAI,CAAC,MAAI,CAACrE,UAAN,IAAoB,CAAC,MAAI,CAACE,OAA9B,EAAuC;;AAEvC,gBAAI;AACA,oBAAM,MAAI,CAACF,UAAL,CAAgB+E,iBAAhB,CAAkC,gBAAlC,EAAoD;AACtDrE,gBAAAA,MAAM,EAAE,MAAI,CAACR,OADyC;AAEtDuD,gBAAAA,IAAI,EAAE,MAAI,CAAC1D;AAF2C,eAApD,CAAN;AAIA4B,cAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ;AACH,aAND,CAME,OAAO+B,KAAP,EAAc;AACZhC,cAAAA,OAAO,CAACgC,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACH;AAX6C;AAYjD;AAED;AACJ;AACA;;;AACiBqB,QAAAA,WAAW,GAAqB;AAAA;;AAAA;AACzC,gBAAI,CAAC,MAAI,CAAC/E,iBAAN,IAA2B,CAAC,MAAI,CAACD,UAArC,EAAiD;AAC7C,qBAAO,KAAP;AACH;;AAED,gBAAI;AACA,kBAAMiF,WAAW,SAAS,MAAI,CAACjF,UAAL,CAAgBkF,KAAhB,EAA1B;AACA,cAAA,MAAI,CAAChF,OAAL,GAAe+E,WAAW,CAACE,IAAZ,IAAoB,EAAnC;;AAEA,kBAAI,MAAI,CAACjF,OAAT,EAAkB;AACdyB,gBAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EADc,CAEd;;AACA,sBAAM,MAAI,CAACwD,oBAAL,EAAN;AACA,uBAAO,IAAP;AACH;AACJ,aAVD,CAUE,OAAOzB,KAAP,EAAc;AACZhC,cAAAA,OAAO,CAACgC,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACH;;AAED,mBAAO,KAAP;AAnByC;AAoB5C;AAED;AACJ;AACA;;;AACkByB,QAAAA,oBAAoB,GAAkB;AAAA;;AAAA;AAChD,gBAAI,CAAC,MAAI,CAACpF,UAAN,IAAoB,CAAC,MAAI,CAACE,OAA9B,EAAuC;;AAEvC,gBAAI;AACA,kBAAMmF,MAAM,SAAS,MAAI,CAACrF,UAAL,CAAgB+E,iBAAhB,CAAkC,gBAAlC,EAAoD;AACrErE,gBAAAA,MAAM,EAAE,MAAI,CAACR;AADwD,eAApD,CAArB;;AAIA,kBAAImF,MAAM,CAAC5B,IAAX,EAAiB;AACb,gBAAA,MAAI,CAAC1D,WAAL,GAAmB,MAAI,CAACmE,gBAAL,CAAsB,MAAI,CAACnE,WAA3B,EAAwCsF,MAAM,CAAC5B,IAA/C,CAAnB;;AACA,gBAAA,MAAI,CAACU,iBAAL;;AACAxC,gBAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ;AACH;AACJ,aAVD,CAUE,OAAO+B,KAAP,EAAc;AACZhC,cAAAA,OAAO,CAACgC,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACH;AAf+C;AAgBnD;AAED;AACJ;AACA;;;AACW2B,QAAAA,qBAAqB,CAACC,QAAD,EAA6C;AACrE,eAAKlF,oBAAL,CAA0BmF,IAA1B,CAA+BD,QAA/B;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,wBAAwB,CAACF,QAAD,EAA6C;AACxE,cAAMG,KAAK,GAAG,KAAKrF,oBAAL,CAA0BsF,OAA1B,CAAkCJ,QAAlC,CAAd;;AACA,cAAIG,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,iBAAKrF,oBAAL,CAA0BuF,MAA1B,CAAiCF,KAAjC,EAAwC,CAAxC;AACH;AACJ;AAED;AACJ;AACA;;;AACYvB,QAAAA,iBAAiB,GAAS;AAC9B,eAAK9D,oBAAL,CAA0BwF,OAA1B,CAAkCN,QAAQ,IAAI;AAC1C,gBAAI;AACAA,cAAAA,QAAQ,CAAC,KAAKxF,WAAN,CAAR;AACH,aAFD,CAEE,OAAO4D,KAAP,EAAc;AACZhC,cAAAA,OAAO,CAACgC,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACH;AACJ,WAND;AAOH,SAhUwC,CAkUzC;;AAEA;AACJ;AACA;;;AACWmC,QAAAA,QAAQ,CAACC,MAAD,EAAuB;AAClC,eAAKhG,WAAL,CAAiB+B,KAAjB,IAA0BiE,MAA1B;AACA,eAAKhG,WAAL,CAAiBoD,UAAjB,CAA4BG,gBAA5B,IAAgDyC,MAAhD;;AACA,eAAK5B,iBAAL;AACH;AAED;AACJ;AACA;;;AACW6B,QAAAA,UAAU,CAACD,MAAD,EAA0B;AACvC,cAAI,KAAKhG,WAAL,CAAiB+B,KAAjB,IAA0BiE,MAA9B,EAAsC;AAClC,iBAAKhG,WAAL,CAAiB+B,KAAjB,IAA0BiE,MAA1B;;AACA,iBAAK5B,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACW8B,QAAAA,aAAa,CAACC,GAAD,EAAoB;AACpC,eAAKnG,WAAL,CAAiBgC,UAAjB,IAA+BmE,GAA/B,CADoC,CAGpC;;AACA,cAAMC,SAAS,GAAG,KAAKpG,WAAL,CAAiB8B,KAAjB,GAAyB,GAA3C,CAJoC,CAIY;;AAChD,cAAI,KAAK9B,WAAL,CAAiBgC,UAAjB,IAA+BoE,SAAnC,EAA8C;AAC1C,iBAAKpG,WAAL,CAAiB8B,KAAjB;AACA,iBAAK9B,WAAL,CAAiBgC,UAAjB,IAA+BoE,SAA/B;AACAxE,YAAAA,OAAO,CAACC,GAAR,qCAAqB,KAAK7B,WAAL,CAAiB8B,KAAtC;AACH;;AAED,eAAKsC,iBAAL;AACH;AAED;AACJ;AACA;;;AACWiC,QAAAA,SAAS,CAACC,KAAD,EAAyB;AACrC,cAAI,KAAKtG,WAAL,CAAiBiC,YAAjB,CAA8B2D,OAA9B,CAAsCU,KAAtC,MAAiD,CAAC,CAAtD,EAAyD;AACrD,iBAAKtG,WAAL,CAAiBiC,YAAjB,CAA8BwD,IAA9B,CAAmCa,KAAnC;;AACA,iBAAKtG,WAAL,CAAiBmC,WAAjB,CAA6BmE,KAA7B,IAAsC;AAClClE,cAAAA,MAAM,EAAE,CAD0B;AAElCC,cAAAA,KAAK,EAAE,CAF2B;AAGlCC,cAAAA,UAAU,EAAE,CAHsB;AAIlCC,cAAAA,KAAK,EAAE;AAJ2B,aAAtC;;AAMA,iBAAK6B,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACWmC,QAAAA,aAAa,CAACD,KAAD,EAAyB;AACzC,iBAAO,KAAKtG,WAAL,CAAiBiC,YAAjB,CAA8B2D,OAA9B,CAAsCU,KAAtC,MAAiD,CAAC,CAAzD;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,aAAa,CAACF,KAAD,EAAyB;AACzC,cAAI,KAAKtG,WAAL,CAAiBiC,YAAjB,CAA8B2D,OAA9B,CAAsCU,KAAtC,MAAiD,CAAC,CAAtD,EAAyD;AACrD,iBAAKtG,WAAL,CAAiBkC,UAAjB,GAA8BoE,KAA9B;;AACA,iBAAKlC,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACWqC,QAAAA,cAAc,CAACH,KAAD,EAAgBI,IAAhB,EAAiD;AAClE,cAAI,CAAC,KAAK1G,WAAL,CAAiBmC,WAAjB,CAA6BmE,KAA7B,CAAL,EAA0C,OAAO,KAAP;AAE1C,cAAMK,OAAO,GAAG,KAAK3G,WAAL,CAAiBmC,WAAjB,CAA6BmE,KAA7B,CAAhB;;AACA,cAAIK,OAAO,CAACD,IAAD,CAAP,GAAgB,CAApB,EAAuB;AACnBC,YAAAA,OAAO,CAACD,IAAD,CAAP;;AACA,iBAAKtC,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACWwC,QAAAA,WAAW,CAACC,OAAD,EAA2B;AACzC,cAAI,KAAK7G,WAAL,CAAiBwC,cAAjB,CAAgCoD,OAAhC,CAAwCiB,OAAxC,MAAqD,CAAC,CAA1D,EAA6D;AACzD,iBAAK7G,WAAL,CAAiBwC,cAAjB,CAAgCiD,IAAhC,CAAqCoB,OAArC;;AACA,iBAAK7G,WAAL,CAAiB0C,aAAjB,CAA+BmE,OAA/B,IAA0C;AACtClE,cAAAA,KAAK,EAAE,CAD+B;AAEtCC,cAAAA,SAAS,EAAE,KAF2B;AAGtCC,cAAAA,QAAQ,EAAE,CAH4B;AAItCC,cAAAA,KAAK,EAAElD,UAAU,CAACmD,CAJoB;AAKtCC,cAAAA,QAAQ,EAAE;AAL4B,aAA1C;;AAOA,iBAAKoB,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACW0C,QAAAA,mBAAmB,CAACD,OAAD,EAAkBE,IAAlB,EAAgCpE,KAAhC,EAAqD;AAC3E;AACA,cAAMG,KAAK,GAAG,KAAKkE,mBAAL,CAAyBD,IAAzB,EAA+BpE,KAA/B,CAAd;;AAEA,cAAI,CAAC,KAAK3C,WAAL,CAAiB0C,aAAjB,CAA+BmE,OAA/B,CAAL,EAA8C;AAC1C,iBAAK7G,WAAL,CAAiB0C,aAAjB,CAA+BmE,OAA/B,IAA0C;AACtClE,cAAAA,KAAK,EAAEA,KAD+B;AAEtCC,cAAAA,SAAS,EAAE,IAF2B;AAGtCC,cAAAA,QAAQ,EAAEkE,IAH4B;AAItCjE,cAAAA,KAAK,EAAEA,KAJ+B;AAKtCE,cAAAA,QAAQ,EAAE;AAL4B,aAA1C;AAOH,WARD,MAQO;AACH,gBAAMiE,QAAQ,GAAG,KAAKjH,WAAL,CAAiB0C,aAAjB,CAA+BmE,OAA/B,CAAjB,CADG,CAGH;;AACA,gBAAIlE,KAAK,GAAGsE,QAAQ,CAACtE,KAAjB,IAA2BA,KAAK,KAAKsE,QAAQ,CAACtE,KAAnB,IAA4BoE,IAAI,GAAGE,QAAQ,CAACpE,QAA3E,EAAsF;AAClFoE,cAAAA,QAAQ,CAACtE,KAAT,GAAiBA,KAAjB;AACAsE,cAAAA,QAAQ,CAACpE,QAAT,GAAoBkE,IAApB;AACAE,cAAAA,QAAQ,CAACnE,KAAT,GAAiBA,KAAjB;AACH;;AAEDmE,YAAAA,QAAQ,CAACrE,SAAT,GAAqB,IAArB;AACAqE,YAAAA,QAAQ,CAACjE,QAAT;AACH,WAxB0E,CA0B3E;;;AACA,eAAKkE,uBAAL,CAA6BL,OAA7B;;AAEA,eAAKzC,iBAAL;AACH;AAED;AACJ;AACA;;;AACY4C,QAAAA,mBAAmB,CAACD,IAAD,EAAepE,KAAf,EAA0C;AACjE;AACA,cAAIA,KAAK,KAAK,CAAd,EAAiB;AACb,gBAAIoE,IAAI,IAAI,KAAZ,EAAmB,OAAOnH,UAAU,CAACuH,CAAlB,CAAnB,CAA6C;AAA7C,iBACK,IAAIJ,IAAI,IAAI,KAAZ,EAAmB,OAAOnH,UAAU,CAACwH,CAAlB,CAAnB,CAAwC;AAAxC,iBACA,OAAOxH,UAAU,CAACyH,CAAlB,CAHQ,CAGiC;AACjD,WAJD,MAIO,IAAI1E,KAAK,KAAK,CAAd,EAAiB;AACpB,gBAAIoE,IAAI,IAAI,KAAZ,EAAmB,OAAOnH,UAAU,CAAC0H,CAAlB,CAAnB,CAA6C;AAA7C,iBACK,OAAO1H,UAAU,CAAC2H,CAAlB,CAFe,CAE0B;AACjD,WAHM,MAGA,IAAI5E,KAAK,KAAK,CAAd,EAAiB;AACpB,mBAAO/C,UAAU,CAAC2H,CAAlB,CADoB,CAC0B;AACjD,WAFM,MAEA;AACH,mBAAO3H,UAAU,CAACmD,CAAlB,CADG,CAC2C;AACjD;AACJ;AAED;AACJ;AACA;;;AACYmE,QAAAA,uBAAuB,CAACM,cAAD,EAA+B;AAC1D,cAAMC,eAAe,GAAG,KAAKzH,WAAL,CAAiB0C,aAAjB,CAA+B8E,cAA/B,CAAxB,CAD0D,CAG1D;;AACA,cAAIC,eAAe,IAAI,KAAKC,eAAL,CAAqBD,eAAe,CAAC3E,KAArC,CAAvB,EAAoE;AAChE,gBAAM6E,WAAW,GAAG,KAAKC,cAAL,CAAoBJ,cAApB,CAApB;;AACA,gBAAIG,WAAW,IAAI,KAAK3H,WAAL,CAAiBwC,cAAjB,CAAgCoD,OAAhC,CAAwC+B,WAAxC,MAAyD,CAAC,CAA7E,EAAgF;AAC5E,mBAAKf,WAAL,CAAiBe,WAAjB;AACA/F,cAAAA,OAAO,CAACC,GAAR,sCAAsB8F,WAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,eAAe,CAAC5E,KAAD,EAA6B;AAChD,iBAAOA,KAAK,KAAKlD,UAAU,CAACmD,CAA5B,CADgD,CACjB;AAClC;AAED;AACJ;AACA;;;AACY6E,QAAAA,cAAc,CAACJ,cAAD,EAAwC;AAC1D;AACA,cAAMK,KAAK,GAAGL,cAAc,CAACK,KAAf,CAAqB,aAArB,CAAd;;AACA,cAAIA,KAAJ,EAAW;AACP,gBAAMC,aAAa,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,CAA9B;AACA,+BAAgBC,aAAa,GAAG,CAAhC;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,gBAAgB,CAACnB,OAAD,EAAwC;AAC3D,iBAAO,KAAK7G,WAAL,CAAiB0C,aAAjB,CAA+BmE,OAA/B,KAA2C,IAAlD;AACH;AAED;AACJ;AACA;;;AACWoB,QAAAA,eAAe,CAACpB,OAAD,EAA2B;AAC7C,iBAAO,KAAK7G,WAAL,CAAiBwC,cAAjB,CAAgCoD,OAAhC,CAAwCiB,OAAxC,MAAqD,CAAC,CAA7D;AACH;AAED;AACJ;AACA;;;AACWqB,QAAAA,iBAAiB,CAACrB,OAAD,EAA0B;AAC9C,cAAMI,QAAQ,GAAG,KAAKe,gBAAL,CAAsBnB,OAAtB,CAAjB;;AACA,cAAI,CAACI,QAAD,IAAa,CAACA,QAAQ,CAACrE,SAA3B,EAAsC;AAClC,mBAAO,EAAP;AACH;;AACD,iBAAOqE,QAAQ,CAACnE,KAAhB;AACH;AAED;AACJ;AACA;;;AACWqF,QAAAA,kBAAkB,CAACrF,KAAD,EAA4B;AACjD,kBAAQA,KAAR;AACI,iBAAKlD,UAAU,CAACuH,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC,iBAAKvH,UAAU,CAACwH,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC,iBAAKxH,UAAU,CAACyH,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC,iBAAKzH,UAAU,CAAC0H,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC,iBAAK1H,UAAU,CAAC2H,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC,iBAAK3H,UAAU,CAACmD,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC;AAAS,qBAAO,SAAP;AAAkB;AAP/B;AASH;AAED;AACJ;AACA;;;AACWqF,QAAAA,cAAc,CAACnF,QAAD,EAAwC;AACzD,eAAKjD,WAAL,CAAiBiD,QAAjB,gBAAiC,KAAKjD,WAAL,CAAiBiD,QAAlD,EAA+DA,QAA/D;;AACA,eAAKmB,iBAAL;AACH;AAED;AACJ;AACA;;;AACWiE,QAAAA,gBAAgB,CAACC,OAAD,EAAyC;AAC5D,eAAKtI,WAAL,CAAiBoD,UAAjB,gBAAmC,KAAKpD,WAAL,CAAiBoD,UAApD,EAAmEkF,OAAnE;;AACA,eAAKlE,iBAAL;AACH;AAED;AACJ;AACA;;;AACWnD,QAAAA,eAAe,GAAS;AAC3B,eAAKC,eAAL;;AACA,eAAKmD,cAAL;;AACA,eAAKD,iBAAL;;AACAxC,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH;AAED;AACJ;AACA;;;AACW0G,QAAAA,gBAAgB,GAAW;AAC9B,iBAAOtE,IAAI,CAACO,SAAL,CAAe,KAAKxE,WAApB,EAAiC,IAAjC,EAAuC,CAAvC,CAAP;AACH;AAED;AACJ;AACA;;;AACWwI,QAAAA,gBAAgB,CAAC1E,QAAD,EAA4B;AAC/C,cAAI;AACA,gBAAMJ,MAAI,GAAGO,IAAI,CAACC,KAAL,CAAWJ,QAAX,CAAb;;AACA,iBAAK9D,WAAL,GAAmB,KAAKmE,gBAAL,CAAsB,KAAKnE,WAA3B,EAAwC0D,MAAxC,CAAnB;AACA,iBAAKW,cAAL;;AACA,iBAAKD,iBAAL;;AACAxC,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA,mBAAO,IAAP;AACH,WAPD,CAOE,OAAO+B,KAAP,EAAc;AACZhC,YAAAA,OAAO,CAACgC,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,mBAAO,KAAP;AACH;AACJ;;AAnmBwC,O,UAC1BpD,S,GAA2B,I", "sourcesContent": ["import { _decorator, Component, Node, sys, game, director } from 'cc';\n\nconst { ccclass, property } = _decorator;\n\n// 声明微信小游戏全局变量\ndeclare const wx: any;\n\n// 玩家数据结构\nexport interface PlayerData {\n    // 基础信息\n    level: number;\n    money: number;\n    experience: number;\n    \n    // 车辆相关\n    unlockedCars: string[];\n    currentCar: string;\n    carUpgrades: { [carId: string]: CarUpgrade };\n    \n    // 关卡相关\n    unlockedLevels: string[];\n    currentLevel: string;\n    levelProgress: { [levelId: string]: LevelProgress };\n    \n    // 设置\n    settings: GameSettings;\n    \n    // 统计信息\n    statistics: GameStatistics;\n    \n    // 时间戳\n    lastSaveTime: number;\n    createTime: number;\n}\n\n// 车辆升级数据\nexport interface CarUpgrade {\n    engine: number;      // 引擎等级 (0-5)\n    tires: number;       // 轮胎等级 (0-5)\n    suspension: number;  // 悬挂等级 (0-5)\n    nitro: number;       // 氮气等级 (0-5)\n}\n\n// 关卡进度数据\nexport interface LevelProgress {\n    stars: number;       // 获得星星数 (0-3)\n    completed: boolean;  // 是否完成\n    bestTime: number;    // 最佳时间 (毫秒)\n    grade: LevelGrade;   // 评级 (S, A, B, C, D, F)\n    attempts: number;    // 尝试次数\n}\n\n// 评级枚举\nexport enum LevelGrade {\n    S = 'S',\n    A = 'A',\n    B = 'B',\n    C = 'C',\n    D = 'D',\n    F = 'F'\n}\n\n// 游戏设置\nexport interface GameSettings {\n    soundVolume: number;     // 音效音量 (0-1)\n    musicVolume: number;     // 音乐音量 (0-1)\n}\n\n// 游戏统计\nexport interface GameStatistics {\n    totalRaces: number;      // 总比赛次数\n    totalWins: number;       // 总胜利次数\n    totalMoneyEarned: number; // 总获得金钱\n}\n\n// 微信小游戏相关接口\ninterface WeChatAPI {\n    setStorageSync(key: string, data: any): void;\n    getStorageSync(key: string): any;\n    removeStorageSync(key: string): void;\n    cloudCallFunction(name: string, data: any): Promise<any>;\n    login(): Promise<any>;\n    getUserInfo(): Promise<any>;\n}\n\n@ccclass('PlayerManager')\nexport class PlayerManager extends Component {\n    private static _instance: PlayerManager = null!;\n    \n    // 存储键名\n    private readonly STORAGE_KEY = 'TopRacing_PlayerData';\n    private readonly STORAGE_KEY_BACKUP = 'TopRacing_PlayerData_Backup';\n    \n    // 玩家数据\n    private _playerData: PlayerData = null!;\n    \n    // 微信API\n    private _wechatAPI: WeChatAPI | null = null;\n    private _isWeChatMiniGame = false;\n    private _userId: string = '';\n    \n    // 自动保存相关\n    private _autoSaveInterval: number = 30000; // 30秒自动保存\n    private _lastAutoSaveTime: number = 0;\n    \n    // 事件回调\n    private _dataChangeCallbacks: ((data: PlayerData) => void)[] = [];\n    \n    public static get instance(): PlayerManager {\n        return PlayerManager._instance;\n    }\n    \n    public get playerData(): PlayerData {\n        return this._playerData;\n    }\n    \n    public get isWeChatMiniGame(): boolean {\n        return this._isWeChatMiniGame;\n    }\n    \n    public get userId(): string {\n        return this._userId;\n    }\n    \n    onLoad() {\n        // 单例模式\n        if (PlayerManager._instance) {\n            this.node.destroy();\n            return;\n        }\n        \n        PlayerManager._instance = this;\n        \n        // 设置为常驻节点，不随场景切换而销毁\n        // (this.node as any)._persistNode = true;\n        director.addPersistRootNode(this.node);\n        \n        // 检测运行环境\n        this._detectEnvironment();\n        this.resetPlayerData();\n        \n        // 初始化玩家数据\n        this._initPlayerData();\n        \n        // 加载数据\n        this.loadPlayerData();\n    }\n    \n    onDestroy() {\n        if (PlayerManager._instance === this) {\n            PlayerManager._instance = null!;\n        }\n    }\n    \n    update(deltaTime: number) {\n        // 自动保存检查\n        const currentTime = Date.now();\n        if (currentTime - this._lastAutoSaveTime > this._autoSaveInterval) {\n            // this.savePlayerData();\n            this._lastAutoSaveTime = currentTime;\n        }\n    }\n    \n    /**\n     * 检测运行环境\n     */\n    private _detectEnvironment() {\n        // 检测是否在微信小游戏环境中\n        if (typeof wx !== 'undefined' && wx.setStorageSync) {\n            this._isWeChatMiniGame = true;\n            this._wechatAPI = wx as any;\n            console.log('检测到微信小游戏环境');\n        } else {\n            this._isWeChatMiniGame = false;\n            console.log('检测到普通游戏环境');\n        }\n    }\n    \n    /**\n     * 初始化玩家数据\n     */\n    private _initPlayerData() {\n        this._playerData = {\n            level: 1,\n            money: 10000,\n            experience: 0,\n            \n            unlockedCars: ['car-1'], // 默认解锁第一辆车\n            currentCar: 'car-1',\n            carUpgrades: {\n                'car-1': {\n                    engine: 0,\n                    tires: 0,\n                    suspension: 0,\n                    nitro: 0\n                }\n            },\n            \n            unlockedLevels: ['level-1'], // 默认只解锁第一关\n            currentLevel: 'level-1',\n            levelProgress: {\n                'level-1': {\n                    stars: 0,\n                    completed: false,\n                    bestTime: 0,\n                    grade: LevelGrade.F,\n                    attempts: 0\n                }\n            },\n            \n            settings: {\n                soundVolume: 0.8,\n                musicVolume: 0.6,\n            },\n            \n            statistics: {\n                totalRaces: 0,\n                totalWins: 0,\n                totalMoneyEarned: 0,\n            },\n            \n            lastSaveTime: Date.now(),\n            createTime: Date.now()\n        };\n    }\n    \n    /**\n     * 加载玩家数据\n     */\n    public async loadPlayerData(): Promise<void> {\n        try {\n            let data: any = null;\n            \n            if (this._isWeChatMiniGame && this._wechatAPI) {\n                // 微信小游戏环境\n                try {\n                    data = this._wechatAPI.getStorageSync(this.STORAGE_KEY);\n                } catch (error) {\n                    console.warn('微信存储读取失败，尝试读取备份:', error);\n                    data = this._wechatAPI.getStorageSync(this.STORAGE_KEY_BACKUP);\n                }\n            } else {\n                // 普通环境使用localStorage\n                const jsonData = sys.localStorage.getItem(this.STORAGE_KEY);\n                if (jsonData) {\n                    data = JSON.parse(jsonData);\n                }\n            }\n            \n            if (data) {\n                // 合并数据，保留新字段的默认值\n                this._playerData = this._mergePlayerData(this._playerData, data);\n                console.log('玩家数据加载成功');\n            } else {\n                console.log('未找到存档数据，使用默认数据');\n            }\n            \n            // 触发数据变化回调\n            this._notifyDataChange();\n            \n        } catch (error) {\n            console.error('加载玩家数据失败:', error);\n        }\n    }\n    \n    /**\n     * 保存玩家数据\n     */\n    public async savePlayerData(): Promise<void> {\n        try {\n            this._playerData.lastSaveTime = Date.now();\n            \n            if (this._isWeChatMiniGame && this._wechatAPI) {\n                // 微信小游戏环境\n                try {\n                    this._wechatAPI.setStorageSync(this.STORAGE_KEY, this._playerData);\n                    // 同时保存备份\n                    this._wechatAPI.setStorageSync(this.STORAGE_KEY_BACKUP, this._playerData);\n                } catch (error) {\n                    console.error('微信存储保存失败:', error);\n                    // 尝试云存储\n                    await this._saveToWeChatCloud();\n                }\n            } else {\n                // 普通环境使用localStorage\n                sys.localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this._playerData));\n            }\n            \n            console.log('玩家数据保存成功');\n            \n        } catch (error) {\n            console.error('保存玩家数据失败:', error);\n        }\n    }\n    \n    /**\n     * 合并玩家数据（处理版本兼容性）\n     */\n    private _mergePlayerData(defaultData: PlayerData, savedData: any): PlayerData {\n        const merged = { ...defaultData };\n        \n        // 递归合并对象\n        for (const key in savedData) {\n            if (savedData.hasOwnProperty(key)) {\n                if (typeof savedData[key] === 'object' && savedData[key] !== null && !Array.isArray(savedData[key])) {\n                    merged[key] = { ...merged[key], ...savedData[key] };\n                } else {\n                    merged[key] = savedData[key];\n                }\n            }\n        }\n        \n        return merged;\n    }\n    \n    /**\n     * 微信云存储保存\n     */\n    private async _saveToWeChatCloud(): Promise<void> {\n        if (!this._wechatAPI || !this._userId) return;\n        \n        try {\n            await this._wechatAPI.cloudCallFunction('savePlayerData', {\n                userId: this._userId,\n                data: this._playerData\n            });\n            console.log('数据已保存到微信云存储');\n        } catch (error) {\n            console.error('微信云存储保存失败:', error);\n        }\n    }\n    \n    /**\n     * 微信登录\n     */\n    public async wechatLogin(): Promise<boolean> {\n        if (!this._isWeChatMiniGame || !this._wechatAPI) {\n            return false;\n        }\n        \n        try {\n            const loginResult = await this._wechatAPI.login();\n            this._userId = loginResult.code || '';\n            \n            if (this._userId) {\n                console.log('微信登录成功');\n                // 登录后尝试从云端加载数据\n                await this._loadFromWeChatCloud();\n                return true;\n            }\n        } catch (error) {\n            console.error('微信登录失败:', error);\n        }\n        \n        return false;\n    }\n    \n    /**\n     * 从微信云端加载数据\n     */\n    private async _loadFromWeChatCloud(): Promise<void> {\n        if (!this._wechatAPI || !this._userId) return;\n        \n        try {\n            const result = await this._wechatAPI.cloudCallFunction('loadPlayerData', {\n                userId: this._userId\n            });\n            \n            if (result.data) {\n                this._playerData = this._mergePlayerData(this._playerData, result.data);\n                this._notifyDataChange();\n                console.log('从微信云端加载数据成功');\n            }\n        } catch (error) {\n            console.error('从微信云端加载数据失败:', error);\n        }\n    }\n    \n    /**\n     * 添加数据变化监听\n     */\n    public addDataChangeListener(callback: (data: PlayerData) => void): void {\n        this._dataChangeCallbacks.push(callback);\n    }\n    \n    /**\n     * 移除数据变化监听\n     */\n    public removeDataChangeListener(callback: (data: PlayerData) => void): void {\n        const index = this._dataChangeCallbacks.indexOf(callback);\n        if (index !== -1) {\n            this._dataChangeCallbacks.splice(index, 1);\n        }\n    }\n    \n    /**\n     * 通知数据变化\n     */\n    private _notifyDataChange(): void {\n        this._dataChangeCallbacks.forEach(callback => {\n            try {\n                callback(this._playerData);\n            } catch (error) {\n                console.error('数据变化回调执行失败:', error);\n            }\n        });\n    }\n    \n    // ==================== 玩家数据操作方法 ====================\n    \n    /**\n     * 增加金钱\n     */\n    public addMoney(amount: number): void {\n        this._playerData.money += amount;\n        this._playerData.statistics.totalMoneyEarned += amount;\n        this._notifyDataChange();\n    }\n    \n    /**\n     * 消费金钱\n     */\n    public spendMoney(amount: number): boolean {\n        if (this._playerData.money >= amount) {\n            this._playerData.money -= amount;\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n    \n    /**\n     * 增加经验\n     */\n    public addExperience(exp: number): void {\n        this._playerData.experience += exp;\n        \n        // 检查是否升级\n        const expNeeded = this._playerData.level * 100; // 每级需要100经验\n        if (this._playerData.experience >= expNeeded) {\n            this._playerData.level++;\n            this._playerData.experience -= expNeeded;\n            console.log(`玩家升级到 ${this._playerData.level} 级！`);\n        }\n        \n        this._notifyDataChange();\n    }\n    \n    /**\n     * 解锁车辆\n     */\n    public unlockCar(carId: string): boolean {\n        if (this._playerData.unlockedCars.indexOf(carId) === -1) {\n            this._playerData.unlockedCars.push(carId);\n            this._playerData.carUpgrades[carId] = {\n                engine: 0,\n                tires: 0,\n                suspension: 0,\n                nitro: 0\n            };\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n\n    /**\n     * 检查车辆是否已解锁\n     */\n    public isCarUnlocked(carId: string): boolean {\n        return this._playerData.unlockedCars.indexOf(carId) !== -1;\n    }\n    \n    /**\n     * 设置当前车辆\n     */\n    public setCurrentCar(carId: string): boolean {\n        if (this._playerData.unlockedCars.indexOf(carId) !== -1) {\n            this._playerData.currentCar = carId;\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n    \n    /**\n     * 升级车辆部件\n     */\n    public upgradeCarPart(carId: string, part: keyof CarUpgrade): boolean {\n        if (!this._playerData.carUpgrades[carId]) return false;\n        \n        const upgrade = this._playerData.carUpgrades[carId];\n        if (upgrade[part] < 5) {\n            upgrade[part]++;\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n    \n    /**\n     * 解锁关卡\n     */\n    public unlockLevel(levelId: string): boolean {\n        if (this._playerData.unlockedLevels.indexOf(levelId) === -1) {\n            this._playerData.unlockedLevels.push(levelId);\n            this._playerData.levelProgress[levelId] = {\n                stars: 0,\n                completed: false,\n                bestTime: 0,\n                grade: LevelGrade.F,\n                attempts: 0\n            };\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n    \n    /**\n     * 更新关卡进度\n     */\n    public updateLevelProgress(levelId: string, time: number, stars: number): void {\n        // 计算评级\n        const grade = this.calculateLevelGrade(time, stars);\n\n        if (!this._playerData.levelProgress[levelId]) {\n            this._playerData.levelProgress[levelId] = {\n                stars: stars,\n                completed: true,\n                bestTime: time,\n                grade: grade,\n                attempts: 1\n            };\n        } else {\n            const progress = this._playerData.levelProgress[levelId];\n\n            // 更新最佳成绩\n            if (stars > progress.stars || (stars === progress.stars && time < progress.bestTime)) {\n                progress.stars = stars;\n                progress.bestTime = time;\n                progress.grade = grade;\n            }\n\n            progress.completed = true;\n            progress.attempts++;\n        }\n\n        // 检查是否解锁下一关卡\n        this.checkAndUnlockNextLevel(levelId);\n\n        this._notifyDataChange();\n    }\n\n    /**\n     * 计算关卡评级\n     */\n    private calculateLevelGrade(time: number, stars: number): LevelGrade {\n        // 基于星星数和时间计算评级\n        if (stars === 3) {\n            if (time <= 30000) return LevelGrade.S;      // 30秒内3星 = S\n            else if (time <= 45000) return LevelGrade.A; // 45秒内3星 = A\n            else return LevelGrade.B;                     // 超过45秒3星 = B\n        } else if (stars === 2) {\n            if (time <= 60000) return LevelGrade.C;      // 60秒内2星 = C\n            else return LevelGrade.D;                     // 超过60秒2星 = D\n        } else if (stars === 1) {\n            return LevelGrade.D;                          // 1星 = D\n        } else {\n            return LevelGrade.F;                          // 0星 = F\n        }\n    }\n\n    /**\n     * 检查并解锁下一关卡\n     */\n    private checkAndUnlockNextLevel(currentLevelId: string): void {\n        const currentProgress = this._playerData.levelProgress[currentLevelId];\n\n        // 只有评级在D及以上时才解锁下一关\n        if (currentProgress && this.isGradePassable(currentProgress.grade)) {\n            const nextLevelId = this.getNextLevelId(currentLevelId);\n            if (nextLevelId && this._playerData.unlockedLevels.indexOf(nextLevelId) === -1) {\n                this.unlockLevel(nextLevelId);\n                console.log(`解锁新关卡: ${nextLevelId}`);\n            }\n        }\n    }\n\n    /**\n     * 检查评级是否达到解锁要求\n     */\n    private isGradePassable(grade: LevelGrade): boolean {\n        return grade !== LevelGrade.F; // D及以上都可以解锁下一关\n    }\n\n    /**\n     * 获取下一关卡ID\n     */\n    private getNextLevelId(currentLevelId: string): string | null {\n        // 假设关卡命名为 level-1, level-2, level-3...\n        const match = currentLevelId.match(/level-(\\d+)/);\n        if (match) {\n            const currentNumber = parseInt(match[1]);\n            return `level-${currentNumber + 1}`;\n        }\n        return null;\n    }\n\n    /**\n     * 获取关卡进度信息\n     */\n    public getLevelProgress(levelId: string): LevelProgress | null {\n        return this._playerData.levelProgress[levelId] || null;\n    }\n\n    /**\n     * 检查关卡是否已解锁\n     */\n    public isLevelUnlocked(levelId: string): boolean {\n        return this._playerData.unlockedLevels.indexOf(levelId) !== -1;\n    }\n\n    /**\n     * 获取关卡评级文本\n     */\n    public getLevelGradeText(levelId: string): string {\n        const progress = this.getLevelProgress(levelId);\n        if (!progress || !progress.completed) {\n            return '';\n        }\n        return progress.grade;\n    }\n\n    /**\n     * 获取关卡评级颜色\n     */\n    public getLevelGradeColor(grade: LevelGrade): string {\n        switch (grade) {\n            case LevelGrade.S: return '#FFD700'; // 金色\n            case LevelGrade.A: return '#C0C0C0'; // 银色\n            case LevelGrade.B: return '#CD7F32'; // 铜色\n            case LevelGrade.C: return '#90EE90'; // 浅绿色\n            case LevelGrade.D: return '#87CEEB'; // 天蓝色\n            case LevelGrade.F: return '#FF6B6B'; // 红色\n            default: return '#FFFFFF'; // 白色\n        }\n    }\n    \n    /**\n     * 更新游戏设置\n     */\n    public updateSettings(settings: Partial<GameSettings>): void {\n        this._playerData.settings = { ...this._playerData.settings, ...settings };\n        this._notifyDataChange();\n    }\n    \n    /**\n     * 更新统计数据\n     */\n    public updateStatistics(updates: Partial<GameStatistics>): void {\n        this._playerData.statistics = { ...this._playerData.statistics, ...updates };\n        this._notifyDataChange();\n    }\n    \n    /**\n     * 重置玩家数据\n     */\n    public resetPlayerData(): void {\n        this._initPlayerData();\n        this.savePlayerData();\n        this._notifyDataChange();\n        console.log('玩家数据已重置');\n    }\n    \n    /**\n     * 导出玩家数据（用于调试）\n     */\n    public exportPlayerData(): string {\n        return JSON.stringify(this._playerData, null, 2);\n    }\n    \n    /**\n     * 导入玩家数据（用于调试）\n     */\n    public importPlayerData(jsonData: string): boolean {\n        try {\n            const data = JSON.parse(jsonData);\n            this._playerData = this._mergePlayerData(this._playerData, data);\n            this.savePlayerData();\n            this._notifyDataChange();\n            console.log('玩家数据导入成功');\n            return true;\n        } catch (error) {\n            console.error('玩家数据导入失败:', error);\n            return false;\n        }\n    }\n} "]}