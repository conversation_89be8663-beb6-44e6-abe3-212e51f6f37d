{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"], "names": ["_decorator", "assert", "Component", "BuiltinPipelineSettings", "EDITOR", "ccclass", "disallowMultiple", "executeInEditMode", "menu", "requireComponent", "BuiltinPipelinePassBuilder", "_parent", "_settings", "getConfigOrder", "getRenderOrder", "onEnable", "getComponent", "getPipelineSettings", "Object", "prototype", "hasOwnProperty", "call", "defineProperty", "value", "configurable", "enumerable", "writable", "_passes", "undefined", "push", "_tryEnableEditorPreview", "onDisable", "passes", "idx", "indexOf", "splice"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAyBIA,MAAAA,U,OAAAA,U;AACAC,MAAAA,M,OAAAA,M;AACAC,MAAAA,S,OAAAA,S;;AAIKC,MAAAA,uB,iBAAAA,uB;;AAEAC,MAAAA,M,UAAAA,M;;;;;;AAjCT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAaM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,gBAAX;AAA6BC,QAAAA,iBAA7B;AAAgDC,QAAAA,IAAhD;AAAsDC,QAAAA;AAAtD,O,GAA2ET,U;;4CAOpEU,0B,WALZL,OAAO,CAAC,4BAAD,C,UACPG,IAAI,CAAC,sCAAD,C,UACJC,gBAAgB;AAAA;AAAA,6D,8CAChBH,gB,UACAC,iB,UAJD,MAKaG,0BALb,SAKgDR,SALhD,CAM6C;AAAA;AAAA;AAAA,eAC/BS,OAD+B;AAAA,eAE/BC,SAF+B;AAAA;;AAGzCC,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDC,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDC,QAAAA,QAAQ,GAAS;AACb,eAAKJ,OAAL,GAAe,KAAKK,YAAL;AAAA;AAAA,iEAAf;AACA,eAAKJ,SAAL,GAAiB,KAAKD,OAAL,CAAaM,mBAAb,EAAjB;;AAEA,cAAI,CAACC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqC,KAAKT,SAA1C,EAAqD,SAArD,CAAL,EAAsE;AAClEM,YAAAA,MAAM,CAACI,cAAP,CAAsB,KAAKV,SAA3B,EAAsC,SAAtC,EAAiD;AAC7CW,cAAAA,KAAK,EAAE,EADsC;AAE7CC,cAAAA,YAAY,EAAE,KAF+B;AAG7CC,cAAAA,UAAU,EAAE,KAHiC;AAI7CC,cAAAA,QAAQ,EAAE;AAJmC,aAAjD;AAMH;;AAEDzB,UAAAA,MAAM,CAAC,KAAKW,SAAL,CAAee,OAAf,KAA2BC,SAA5B,CAAN;;AACA,eAAKhB,SAAL,CAAee,OAAf,CAAuBE,IAAvB,CAA4B,IAA5B;;AAEA,cAAIzB,MAAJ,EAAY;AACR,iBAAKO,OAAL,CAAamB,uBAAb;AACH;AACJ;;AACDC,QAAAA,SAAS,GAAS;AACd9B,UAAAA,MAAM,CAACiB,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqC,KAAKT,SAA1C,EAAqD,SAArD,CAAD,CAAN;AACA,cAAMoB,MAAM,GAAG,KAAKpB,SAAL,CAAee,OAA9B;AACA1B,UAAAA,MAAM,CAAC+B,MAAM,KAAKJ,SAAZ,CAAN;AACA,cAAMK,GAAG,GAAGD,MAAM,CAACE,OAAP,CAAe,IAAf,CAAZ;AACAjC,UAAAA,MAAM,CAACgC,GAAG,IAAI,CAAR,CAAN;AACAD,UAAAA,MAAM,CAACG,MAAP,CAAcF,GAAd,EAAmB,CAAnB;AACH;;AApCwC,O", "sourcesContent": ["/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com/\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\nimport {\n    _decorator,\n    assert,\n    Component,\n    rendering,\n} from 'cc';\n\nimport { BuiltinPipelineSettings } from './builtin-pipeline-settings';\nimport { PipelineSettings2 } from './builtin-pipeline';\nimport { EDITOR } from 'cc/env';\n\nconst { ccclass, disallowMultiple, executeInEditMode, menu, requireComponent } = _decorator;\n\n@ccclass('BuiltinPipelinePassBuilder')\n@menu('Rendering/BuiltinPipelinePassBuilder')\n@requireComponent(BuiltinPipelineSettings)\n@disallowMultiple\n@executeInEditMode\nexport class BuiltinPipelinePassBuilder extends Component\n    implements rendering.PipelinePassBuilder {\n    protected _parent!: BuiltinPipelineSettings;\n    protected _settings!: PipelineSettings2;\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 200;\n    }\n    onEnable(): void {\n        this._parent = this.getComponent(BuiltinPipelineSettings)!;\n        this._settings = this._parent.getPipelineSettings();\n\n        if (!Object.prototype.hasOwnProperty.call(this._settings, '_passes')) {\n            Object.defineProperty(this._settings, '_passes', {\n                value: [],\n                configurable: false,\n                enumerable: false,\n                writable: true,\n            });\n        }\n\n        assert(this._settings._passes !== undefined);\n        this._settings._passes.push(this);\n\n        if (EDITOR) {\n            this._parent._tryEnableEditorPreview();\n        }\n    }\n    onDisable(): void {\n        assert(Object.prototype.hasOwnProperty.call(this._settings, '_passes'));\n        const passes = this._settings._passes;\n        assert(passes !== undefined);\n        const idx = passes.indexOf(this);\n        assert(idx >= 0);\n        passes.splice(idx, 1);\n    }\n}\n"]}