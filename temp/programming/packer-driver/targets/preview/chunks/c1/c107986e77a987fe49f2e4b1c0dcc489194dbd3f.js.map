{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts"], "names": ["Color", "<PERSON><PERSON>", "UITransform", "instantiate", "Toggle", "_decorator", "Component", "<PERSON><PERSON>", "director", "Node", "Label", "RichText", "ccclass", "property", "DebugViewRuntimeControl", "_single", "str<PERSON><PERSON>le", "strComposite", "strMisc", "compositeModeToggleList", "singleModeToggleList", "miscModeToggleList", "textComponentList", "labelComponentList", "textContentList", "hideButtonLabel", "_currentColorIndex", "strColor", "color", "WHITE", "BLACK", "RED", "GREEN", "BLUE", "start", "canvas", "node", "parent", "getComponent", "console", "error", "uiTransform", "halfScreenWidth", "width", "halfScreenHeight", "height", "x", "y", "miscNode", "getChildByName", "buttonNode", "name", "titleNode", "i", "new<PERSON>abel", "EnableAllCompositeModeButton", "setPosition", "setScale", "labelComponent", "string", "overflow", "length", "currentRow", "newNode", "singleModeToggle", "textComponent", "getComponentInChildren", "on", "EventType", "TOGGLE", "toggleSingleMode", "CLICK", "enableAllCompositeMode", "changeColorButton", "changeTextColor", "HideButton", "hideUI", "compositeModeToggle", "toggleComponent", "isChecked", "toggleLightingWithAlbedo", "toggleCSMColoration", "toggleCompositeMode", "isTextMatched", "textUI", "textDescription", "tempText", "String", "findIndex", "search", "substr", "toggle", "debugView", "root", "singleMode", "enableCompositeMode", "lightingWithAlbedo", "csmLayerColoration", "button", "activeValue", "active", "onLoad", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,M,OAAAA,M;AAAqBC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAA8BC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAwCC,MAAAA,K,OAAAA,K;AAAwBC,MAAAA,Q,OAAAA,Q;;;;;;;;;OACtM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yCAGjBS,uB,WADZF,OAAO,CAAC,kCAAD,C,UAEHC,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACJ,IAAD,C,2BANb,MACaK,uBADb,SAC6CR,SAD7C,CACuD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAOtDS,OAPsD,GAOpC,CAPoC;AAAA,eAS3CC,SAT2C,GASrB,CAC1B,iBAD0B,EAE1B,cAF0B,EAG1B,eAH0B,EAI1B,gBAJ0B,EAK1B,gBAL0B,EAM1B,eAN0B,EAO1B,WAP0B,EAQ1B,KAR0B,EAS1B,KAT0B,EAU1B,aAV0B,EAW1B,eAX0B,EAY1B,cAZ0B,EAc1B,iBAd0B,EAe1B,kBAf0B,EAgB1B,mBAhB0B,EAiB1B,YAjB0B,EAkB1B,eAlB0B,EAmB1B,gBAnB0B,EAoB1B,cApB0B,EAqB1B,UArB0B,EAsB1B,WAtB0B,EAuB1B,oBAvB0B,EAwB1B,KAxB0B,EA0B1B,gBA1B0B,EA2B1B,iBA3B0B,EA4B1B,YA5B0B,EA6B1B,aA7B0B,EA8B1B,cA9B0B,EA+B1B,SA/B0B,EAgC1B,UAhC0B,EAiC1B,WAjC0B,EAkC1B,QAlC0B,EAmC1B,IAnC0B,EAqC1B,SArC0B,EAsC1B,yBAtC0B,EAuC1B,0BAvC0B,EAwC1B,sBAxC0B,EAyC1B,uBAzC0B,EA0C1B,cA1C0B,EA2C1B,0BA3C0B,EA4C1B,uBA5C0B,EA6C1B,cA7C0B,EA+C1B,KA/C0B,CATqB;AAAA,eA0D3CC,YA1D2C,GA0DlB,CAC7B,gBAD6B,EAE7B,iBAF6B,EAG7B,aAH6B,EAI7B,cAJ6B,EAK7B,UAL6B,EAM7B,WAN6B,EAO7B,QAP6B,EAQ7B,IAR6B,EAU7B,YAV6B,EAW7B,KAX6B,EAa7B,cAb6B,EAc7B,kBAd6B,EAgB7B,SAhB6B,EAiB7B,kBAjB6B,EAkB7B,mBAlB6B,EAmB7B,mBAnB6B,EAoB7B,IApB6B,CA1DkB;AAAA,eAgF3CC,OAhF2C,GAgFvB,CACxB,sBADwB,EAExB,sBAFwB,CAhFuB;AAAA,eAqF3CC,uBArF2C,GAqFT,EArFS;AAAA,eAsF3CC,oBAtF2C,GAsFZ,EAtFY;AAAA,eAuF3CC,kBAvF2C,GAuFd,EAvFc;AAAA,eAwF3CC,iBAxF2C,GAwFX,EAxFW;AAAA,eAyF3CC,kBAzF2C,GAyFb,EAzFa;AAAA,eA0F3CC,eA1F2C,GA0Ff,EA1Fe;AAAA,eA2F3CC,eA3F2C;AAAA,eAyR3CC,kBAzR2C,GAyRtB,CAzRsB;AAAA,eA0R3CC,QA1R2C,GA0RtB,CACzB,iBADyB,EAEzB,iBAFyB,EAGzB,iBAHyB,EAIzB,iBAJyB,EAKzB,iBALyB,CA1RsB;AAAA,eAiS3CC,KAjS2C,GAiS1B,CACrB5B,KAAK,CAAC6B,KADe,EAErB7B,KAAK,CAAC8B,KAFe,EAGrB9B,KAAK,CAAC+B,GAHe,EAIrB/B,KAAK,CAACgC,KAJe,EAKrBhC,KAAK,CAACiC,IALe,CAjS0B;AAAA;;AA4FnDC,QAAAA,KAAK,GAAG;AACJ;AACA,cAAMC,MAAM,GAAG,KAAKC,IAAL,CAAUC,MAAV,CAAiBC,YAAjB,CAA8BrC,MAA9B,CAAf;;AACA,cAAI,CAACkC,MAAL,EAAa;AACTI,YAAAA,OAAO,CAACC,KAAR,CAAc,sDAAd;AACA;AACH;;AAED,cAAMC,WAAW,GAAG,KAAKL,IAAL,CAAUC,MAAV,CAAiBC,YAAjB,CAA8BpC,WAA9B,CAApB;AACA,cAAMwC,eAAe,GAAGD,WAAW,CAACE,KAAZ,GAAoB,GAA5C;AACA,cAAMC,gBAAgB,GAAGH,WAAW,CAACI,MAAZ,GAAqB,GAA9C;AAEA,cAAIC,CAAC,GAAG,CAACJ,eAAD,GAAmBA,eAAe,GAAG,GAA7C;AAAA,cAAkDK,CAAC,GAAGH,gBAAgB,GAAGA,gBAAgB,GAAG,GAA5F;AACA,cAAMD,KAAK,GAAG,GAAd;AAAA,cAAmBE,MAAM,GAAG,EAA5B,CAbI,CAeJ;;AACA,cAAMG,QAAQ,GAAG,KAAKZ,IAAL,CAAUa,cAAV,CAAyB,UAAzB,CAAjB;AACA,cAAMC,UAAU,GAAG/C,WAAW,CAAC6C,QAAD,CAA9B;AACAE,UAAAA,UAAU,CAACb,MAAX,GAAoB,KAAKD,IAAzB;AACAc,UAAAA,UAAU,CAACC,IAAX,GAAkB,SAAlB;AACA,cAAMC,SAAS,GAAGjD,WAAW,CAAC6C,QAAD,CAA7B;AACAI,UAAAA,SAAS,CAACf,MAAV,GAAmB,KAAKD,IAAxB;AACAgB,UAAAA,SAAS,CAACD,IAAV,GAAiB,QAAjB,CAtBI,CAwBJ;;AACA,eAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,gBAAMC,QAAQ,GAAGnD,WAAW,CAAC,KAAKoD,4BAAL,CAAkCN,cAAlC,CAAiD,OAAjD,CAAD,CAA5B;AACAK,YAAAA,QAAQ,CAACE,WAAT,CAAqBV,CAAC,IAAIO,CAAC,GAAG,CAAJ,GAAQ,KAAKV,KAAK,GAAG,CAArB,GAAyB,GAA7B,CAAtB,EAAyDI,CAAzD,EAA4D,GAA5D;AACAO,YAAAA,QAAQ,CAACG,QAAT,CAAkB,IAAlB,EAAwB,IAAxB,EAA8B,IAA9B;AACAH,YAAAA,QAAQ,CAACjB,MAAT,GAAkBe,SAAlB;;AACA,gBAAMM,eAAc,GAAGJ,QAAQ,CAAChB,YAAT,CAAsB5B,KAAtB,CAAvB;;AACAgD,YAAAA,eAAc,CAACC,MAAf,GAAwBN,CAAC,GAAG,oCAAH,GAA0C,iCAAnE;AACAK,YAAAA,eAAc,CAAC9B,KAAf,GAAuB5B,KAAK,CAAC6B,KAA7B;AACA6B,YAAAA,eAAc,CAACE,QAAf,GAA0B,CAA1B;AACA,iBAAKrC,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,eAA1D;AACH;;AAEDX,UAAAA,CAAC,IAAIF,MAAL,CArCI,CAsCJ;;AACA,cAAIiB,UAAU,GAAG,CAAjB;;AACA,eAAK,IAAIT,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKrC,SAAL,CAAe6C,MAAnC,EAA2CR,EAAC,IAAIS,UAAU,EAA1D,EAA8D;AAC1D,gBAAIT,EAAC,KAAK,KAAKrC,SAAL,CAAe6C,MAAf,IAAyB,CAAnC,EAAsC;AAClCf,cAAAA,CAAC,IAAIH,KAAL;AACAmB,cAAAA,UAAU,GAAG,CAAb;AACH;;AACD,gBAAMC,OAAO,GAAGV,EAAC,GAAGlD,WAAW,CAAC,KAAK6D,gBAAN,CAAd,GAAwC,KAAKA,gBAA9D;AACAD,YAAAA,OAAO,CAACP,WAAR,CAAoBV,CAApB,EAAuBC,CAAC,GAAGF,MAAM,GAAGiB,UAApC,EAAgD,GAAhD;AACAC,YAAAA,OAAO,CAACN,QAAR,CAAiB,GAAjB,EAAsB,GAAtB,EAA2B,GAA3B;AACAM,YAAAA,OAAO,CAAC1B,MAAR,GAAiB,KAAK2B,gBAAL,CAAsB3B,MAAvC;AAEA,gBAAM4B,aAAa,GAAGF,OAAO,CAACG,sBAAR,CAA+BvD,QAA/B,CAAtB;AACAsD,YAAAA,aAAa,CAACN,MAAd,GAAuB,KAAK3C,SAAL,CAAeqC,EAAf,CAAvB;AACA,iBAAK/B,iBAAL,CAAuB,KAAKA,iBAAL,CAAuBuC,MAA9C,IAAwDI,aAAxD;AACA,iBAAKzC,eAAL,CAAqB,KAAKA,eAAL,CAAqBqC,MAA1C,IAAoDI,aAAa,CAACN,MAAlE;AAEAI,YAAAA,OAAO,CAACI,EAAR,CAAW/D,MAAM,CAACgE,SAAP,CAAiBC,MAA5B,EAAoC,KAAKC,gBAAzC,EAA2D,IAA3D;AAEA,iBAAKlD,oBAAL,CAA0BiC,EAA1B,IAA+BU,OAA/B;AACH;;AAEDjB,UAAAA,CAAC,IAAIH,KAAL,CA5DI,CA6DJ;;AACA,eAAKY,4BAAL,CAAkCC,WAAlC,CAA8CV,CAAC,GAAG,EAAlD,EAAsDC,CAAtD,EAAyD,GAAzD;AACA,eAAKQ,4BAAL,CAAkCE,QAAlC,CAA2C,GAA3C,EAAgD,GAAhD,EAAqD,GAArD;AACA,eAAKF,4BAAL,CAAkCY,EAAlC,CAAqC5D,MAAM,CAAC6D,SAAP,CAAiBG,KAAtD,EAA6D,KAAKC,sBAAlE,EAA0F,IAA1F;AACA,eAAKjB,4BAAL,CAAkClB,MAAlC,GAA2Ca,UAA3C;AACA,cAAIQ,cAAc,GAAG,KAAKH,4BAAL,CAAkCW,sBAAlC,CAAyDxD,KAAzD,CAArB;AACA,eAAKa,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,cAA1D;AAEA,cAAMe,iBAAiB,GAAGtE,WAAW,CAAC,KAAKoD,4BAAN,CAArC;AACAkB,UAAAA,iBAAiB,CAACjB,WAAlB,CAA8BV,CAAC,GAAG,EAAlC,EAAsCC,CAAtC,EAAyC,GAAzC;AACA0B,UAAAA,iBAAiB,CAAChB,QAAlB,CAA2B,GAA3B,EAAgC,GAAhC,EAAqC,GAArC;AACAgB,UAAAA,iBAAiB,CAACN,EAAlB,CAAqB5D,MAAM,CAAC6D,SAAP,CAAiBG,KAAtC,EAA6C,KAAKG,eAAlD,EAAmE,IAAnE;AACAD,UAAAA,iBAAiB,CAACpC,MAAlB,GAA2Ba,UAA3B;AACAQ,UAAAA,cAAc,GAAGe,iBAAiB,CAACP,sBAAlB,CAAyCxD,KAAzC,CAAjB;AACAgD,UAAAA,cAAc,CAACC,MAAf,GAAwB,WAAxB;AACA,eAAKpC,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,cAA1D;AAEA,cAAMiB,UAAU,GAAGxE,WAAW,CAAC,KAAKoD,4BAAN,CAA9B;AACAoB,UAAAA,UAAU,CAACnB,WAAX,CAAuBV,CAAC,GAAG,GAA3B,EAAgCC,CAAhC,EAAmC,GAAnC;AACA4B,UAAAA,UAAU,CAAClB,QAAX,CAAoB,GAApB,EAAyB,GAAzB,EAA8B,GAA9B;AACAkB,UAAAA,UAAU,CAACR,EAAX,CAAc5D,MAAM,CAAC6D,SAAP,CAAiBG,KAA/B,EAAsC,KAAKK,MAA3C,EAAmD,IAAnD;AACAD,UAAAA,UAAU,CAACtC,MAAX,GAAoB,KAAKD,IAAL,CAAUC,MAA9B;AACAqB,UAAAA,cAAc,GAAGiB,UAAU,CAACT,sBAAX,CAAkCxD,KAAlC,CAAjB;AACAgD,UAAAA,cAAc,CAACC,MAAf,GAAwB,SAAxB;AACA,eAAKpC,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,cAA1D;AACA,eAAKjC,eAAL,GAAuBiC,cAAvB,CAtFI,CAwFJ;;AACAX,UAAAA,CAAC,IAAI,EAAL;;AACA,eAAK,IAAIM,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKnC,OAAL,CAAa2C,MAAjC,EAAyCR,GAAC,EAA1C,EAA8C;AAC1C,gBAAMU,QAAO,GAAG5D,WAAW,CAAC,KAAK0E,mBAAN,CAA3B;;AACAd,YAAAA,QAAO,CAACP,WAAR,CAAoBV,CAApB,EAAuBC,CAAC,GAAGF,MAAM,GAAGQ,GAApC,EAAuC,GAAvC;;AACAU,YAAAA,QAAO,CAACN,QAAR,CAAiB,GAAjB,EAAsB,GAAtB,EAA2B,GAA3B;;AACAM,YAAAA,QAAO,CAAC1B,MAAR,GAAiBW,QAAjB;;AAEA,gBAAMiB,cAAa,GAAGF,QAAO,CAACG,sBAAR,CAA+BvD,QAA/B,CAAtB;;AACAsD,YAAAA,cAAa,CAACN,MAAd,GAAuB,KAAKzC,OAAL,CAAamC,GAAb,CAAvB;AACA,iBAAK/B,iBAAL,CAAuB,KAAKA,iBAAL,CAAuBuC,MAA9C,IAAwDI,cAAxD;AACA,iBAAKzC,eAAL,CAAqB,KAAKA,eAAL,CAAqBqC,MAA1C,IAAoDI,cAAa,CAACN,MAAlE;;AAEA,gBAAMmB,eAAe,GAAGf,QAAO,CAACzB,YAAR,CAAqBlC,MAArB,CAAxB;;AACA0E,YAAAA,eAAe,CAACC,SAAhB,GAA4B1B,GAAC,GAAG,IAAH,GAAU,KAAvC;;AACAU,YAAAA,QAAO,CAACI,EAAR,CAAW/D,MAAM,CAACgE,SAAP,CAAiBC,MAA5B,EAAoChB,GAAC,GAAG,KAAK2B,wBAAR,GAAmC,KAAKC,mBAA7E,EAAkG,IAAlG;;AACA,iBAAK5D,kBAAL,CAAwBgC,GAAxB,IAA6BU,QAA7B;AACH,WAzGG,CA2GJ;;;AACAhB,UAAAA,CAAC,IAAI,GAAL;;AACA,eAAK,IAAIM,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKpC,YAAL,CAAkB4C,MAAtC,EAA8CR,GAAC,EAA/C,EAAmD;AAC/C,gBAAMU,SAAO,GAAGV,GAAC,GAAGlD,WAAW,CAAC,KAAK0E,mBAAN,CAAd,GAA2C,KAAKA,mBAAjE;;AACAd,YAAAA,SAAO,CAACP,WAAR,CAAoBV,CAApB,EAAuBC,CAAC,GAAGF,MAAM,GAAGQ,GAApC,EAAuC,GAAvC;;AACAU,YAAAA,SAAO,CAACN,QAAR,CAAiB,GAAjB,EAAsB,GAAtB,EAA2B,GAA3B;;AACAM,YAAAA,SAAO,CAAC1B,MAAR,GAAiB,KAAKwC,mBAAL,CAAyBxC,MAA1C;;AAEA,gBAAM4B,eAAa,GAAGF,SAAO,CAACG,sBAAR,CAA+BvD,QAA/B,CAAtB;;AACAsD,YAAAA,eAAa,CAACN,MAAd,GAAuB,KAAK1C,YAAL,CAAkBoC,GAAlB,CAAvB;AACA,iBAAK/B,iBAAL,CAAuB,KAAKA,iBAAL,CAAuBuC,MAA9C,IAAwDI,eAAxD;AACA,iBAAKzC,eAAL,CAAqB,KAAKA,eAAL,CAAqBqC,MAA1C,IAAoDI,eAAa,CAACN,MAAlE;;AAEAI,YAAAA,SAAO,CAACI,EAAR,CAAW/D,MAAM,CAACgE,SAAP,CAAiBC,MAA5B,EAAoC,KAAKa,mBAAzC,EAA8D,IAA9D;;AAEA,iBAAK/D,uBAAL,CAA6BkC,GAA7B,IAAkCU,SAAlC;AACH;AACJ;;AAEDoB,QAAAA,aAAa,CAACC,MAAD,EAASC,eAAT,EAAoC;AAC7C,cAAIC,QAAQ,GAAG,IAAIC,MAAJ,CAAWH,MAAX,CAAf;AACA,cAAMI,SAAS,GAAGF,QAAQ,CAACG,MAAT,CAAgB,GAAhB,CAAlB;;AACA,cAAID,SAAS,KAAK,CAAC,CAAnB,EAAsB;AAClB,mBAAOJ,MAAM,KAAKC,eAAlB;AACH,WAFD,MAEO;AACHC,YAAAA,QAAQ,GAAGA,QAAQ,CAACI,MAAT,CAAgBF,SAAS,GAAG,CAA5B,CAAX;AACAF,YAAAA,QAAQ,GAAGA,QAAQ,CAACI,MAAT,CAAgB,CAAhB,EAAmBJ,QAAQ,CAACG,MAAT,CAAgB,GAAhB,CAAnB,CAAX;AACA,mBAAOH,QAAQ,KAAKD,eAApB;AACH;AACJ;;AACDf,QAAAA,gBAAgB,CAACqB,MAAD,EAAiB;AAC7B,cAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACA,cAAM3B,aAAa,GAAG0B,MAAM,CAACzB,sBAAP,CAA8BvD,QAA9B,CAAtB;;AACA,eAAK,IAAI0C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrC,SAAL,CAAe6C,MAAnC,EAA2CR,CAAC,EAA5C,EAAgD;AAC5C,gBAAI,KAAK8B,aAAL,CAAmBlB,aAAa,CAACN,MAAjC,EAAyC,KAAK3C,SAAL,CAAeqC,CAAf,CAAzC,CAAJ,EAAiE;AAC7DuC,cAAAA,SAAS,CAACE,UAAV,GAAuBzC,CAAvB;AACH;AACJ;AACJ;;AACD6B,QAAAA,mBAAmB,CAACS,MAAD,EAAiB;AAChC,cAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACA,cAAM3B,aAAa,GAAG0B,MAAM,CAACzB,sBAAP,CAA8BvD,QAA9B,CAAtB;;AACA,eAAK,IAAI0C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpC,YAAL,CAAkB4C,MAAtC,EAA8CR,CAAC,EAA/C,EAAmD;AAC/C,gBAAI,KAAK8B,aAAL,CAAmBlB,aAAa,CAACN,MAAjC,EAAyC,KAAK1C,YAAL,CAAkBoC,CAAlB,CAAzC,CAAJ,EAAoE;AAChEuC,cAAAA,SAAS,CAACG,mBAAV,CAA8B1C,CAA9B,EAAiCsC,MAAM,CAACZ,SAAxC;AACH;AACJ;AACJ;;AACDC,QAAAA,wBAAwB,CAACW,MAAD,EAAiB;AACrC,cAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACAA,UAAAA,SAAS,CAACI,kBAAV,GAA+BL,MAAM,CAACZ,SAAtC;AACH;;AACDE,QAAAA,mBAAmB,CAACU,MAAD,EAAiB;AAChC,cAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACAA,UAAAA,SAAS,CAACK,kBAAV,GAA+BN,MAAM,CAACZ,SAAtC;AACH;;AACDP,QAAAA,sBAAsB,CAAC0B,MAAD,EAAiB;AACnC,cAAMN,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACAA,UAAAA,SAAS,CAACpB,sBAAV,CAAiC,IAAjC;;AACA,eAAK,IAAInB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlC,uBAAL,CAA6B0C,MAAjD,EAAyDR,CAAC,EAA1D,EAA8D;AAC1D,gBAAMyB,gBAAe,GAAG,KAAK3D,uBAAL,CAA6BkC,CAA7B,EAAgCf,YAAhC,CAA6ClC,MAA7C,CAAxB;;AACA0E,YAAAA,gBAAe,CAACC,SAAhB,GAA4B,IAA5B;AACH;;AAED,cAAID,eAAe,GAAG,KAAKzD,kBAAL,CAAwB,CAAxB,EAA2BiB,YAA3B,CAAwClC,MAAxC,CAAtB;AACA0E,UAAAA,eAAe,CAACC,SAAhB,GAA4B,KAA5B;AACAa,UAAAA,SAAS,CAACK,kBAAV,GAA+B,KAA/B;AACAnB,UAAAA,eAAe,GAAG,KAAKzD,kBAAL,CAAwB,CAAxB,EAA2BiB,YAA3B,CAAwClC,MAAxC,CAAlB;AACA0E,UAAAA,eAAe,CAACC,SAAhB,GAA4B,IAA5B;AACAa,UAAAA,SAAS,CAACI,kBAAV,GAA+B,IAA/B;AACH;;AACDpB,QAAAA,MAAM,CAACsB,MAAD,EAAiB;AACnB,cAAM9C,SAAS,GAAG,KAAKhB,IAAL,CAAUa,cAAV,CAAyB,QAAzB,CAAlB;AACA,cAAMkD,WAAW,GAAG,CAAC/C,SAAS,CAACgD,MAA/B;AACA,eAAKhF,oBAAL,CAA0B,CAA1B,EAA6BiB,MAA7B,CAAoC+D,MAApC,GAA6CD,WAA7C;AACA,eAAK9E,kBAAL,CAAwB,CAAxB,EAA2BgB,MAA3B,CAAkC+D,MAAlC,GAA2CD,WAA3C;AACA,eAAKhF,uBAAL,CAA6B,CAA7B,EAAgCkB,MAAhC,CAAuC+D,MAAvC,GAAgDD,WAAhD;AACA,eAAK5C,4BAAL,CAAkClB,MAAlC,CAAyC+D,MAAzC,GAAkDD,WAAlD;AACA/C,UAAAA,SAAS,CAACgD,MAAV,GAAmBD,WAAnB;AACA,eAAK1E,eAAL,CAAqBkC,MAArB,GAA8BwC,WAAW,GAAG,SAAH,GAAe,SAAxD;AACH;;AAiBDzB,QAAAA,eAAe,CAACwB,MAAD,EAAiB;AAC5B,eAAKxE,kBAAL;;AACA,cAAI,KAAKA,kBAAL,IAA2B,KAAKC,QAAL,CAAckC,MAA7C,EAAqD;AACjD,iBAAKnC,kBAAL,GAA0B,CAA1B;AACH;;AACD,eAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/B,iBAAL,CAAuBuC,MAA3C,EAAmDR,CAAC,EAApD,EAAwD;AACpD,iBAAK/B,iBAAL,CAAuB+B,CAAvB,EAA0BM,MAA1B,GAAmC,KAAKhC,QAAL,CAAc,KAAKD,kBAAnB,IAAyC,KAAKF,eAAL,CAAqB6B,CAArB,CAAzC,GAAmE,UAAtG;AACH;;AACD,eAAK,IAAIA,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAK9B,kBAAL,CAAwBsC,MAA5C,EAAoDR,GAAC,EAArD,EAAyD;AACrD,iBAAK9B,kBAAL,CAAwB8B,GAAxB,EAA2BzB,KAA3B,GAAmC,KAAKA,KAAL,CAAW,KAAKF,kBAAhB,CAAnC;AACH;AACJ;;AAED2E,QAAAA,MAAM,GAAG,CACR;;AACDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CACzB;;AAxTkD,O;;;;;iBAEhB,I;;;;;;;iBAEH,I;;;;;;;iBAEY,I", "sourcesContent": ["import { Color, Canvas, UITransform, instantiate, math, Toggle, TextureCube, _decorator, Component, But<PERSON>, label<PERSON><PERSON>mbler, game, director, Node, Scene, renderer, CameraComponent, Label, ForwardPipeline, RichText } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('internal.DebugViewRuntimeControl')\nexport class DebugViewRuntimeControl extends Component {\n    @property(Node)\n    compositeModeToggle: Node | null = null;\n    @property(Node)\n    singleModeToggle: Node | null = null;\n    @property(Node)\n    EnableAllCompositeModeButton: Node | null = null;\n\t_single: number = 0;\n\n    private strSingle: string[] = [\n        'No Single Debug',\n        'Vertex Color',\n        'Vertex Normal',\n        'Vertex Tangent',\n        'World Position',\n        'Vertex Mirror',\n        'Face Side',\n        'UV0',\n        'UV1',\n        'UV Lightmap',\n        'Project Depth',\n        'Linear Depth',\n\n        'Fragment Normal',\n        'Fragment Tangent',\n        'Fragment Binormal',\n        'Base Color',\n        'Diffuse Color',\n        'Specular Color',\n        'Transparency',\n        'Metallic',\n        'Roughness',\n        'Specular Intensity',\n        'IOR',\n\n        'Direct Diffuse',\n        'Direct Specular',\n        'Direct All',\n        'Env Diffuse',\n        'Env Specular',\n        'Env All',\n        'Emissive',\n        'Light Map',\n        'Shadow',\n        'AO',\n\n        'Fresnel',\n        'Direct Transmit Diffuse',\n        'Direct Transmit Specular',\n        'Env Transmit Diffuse',\n        'Env Transmit Specular',\n        'Transmit All',\n        'Direct Internal Specular',\n        'Env Internal Specular',\n        'Internal All',\n\n        'Fog',\n    ];\n    private strComposite: string[] = [\n        'Direct Diffuse',\n        'Direct Specular',\n        'Env Diffuse',\n        'Env Specular',\n        'Emissive',\n        'Light Map',\n        'Shadow',\n        'AO',\n\n        'Normal Map',\n        'Fog',\n\n        'Tone Mapping',\n        'Gamma Correction',\n\n        'Fresnel',\n        'Transmit Diffuse',\n        'Transmit Specular',\n        'Internal Specular',\n        'TT',\n    ];\n    private strMisc: string[] = [\n        'CSM Layer Coloration',\n        'Lighting With Albedo',\n    ];\n\n    private compositeModeToggleList: Node[] = [];\n    private singleModeToggleList: Node[] = [];\n    private miscModeToggleList: Node[] = [];\n    private textComponentList: RichText[] = [];\n    private labelComponentList: Label[] = [];\n    private textContentList: string[] = [];\n    private hideButtonLabel: Label;\n    start() {\n        // get canvas resolution\n        const canvas = this.node.parent.getComponent(Canvas);\n        if (!canvas) {\n            console.error('debug-view-runtime-control should be child of Canvas');\n            return;\n        }\n\n        const uiTransform = this.node.parent.getComponent(UITransform);\n        const halfScreenWidth = uiTransform.width * 0.5;\n        const halfScreenHeight = uiTransform.height * 0.5;\n\n        let x = -halfScreenWidth + halfScreenWidth * 0.1, y = halfScreenHeight - halfScreenHeight * 0.1;\n        const width = 200, height = 20;\n\n        // new nodes\n        const miscNode = this.node.getChildByName('MiscMode');\n        const buttonNode = instantiate(miscNode);\n        buttonNode.parent = this.node;\n        buttonNode.name = 'Buttons';\n        const titleNode = instantiate(miscNode);\n        titleNode.parent = this.node;\n        titleNode.name = 'Titles';\n\n        // title\n        for (let i = 0; i < 2; i++) {\n            const newLabel = instantiate(this.EnableAllCompositeModeButton.getChildByName('Label'));\n            newLabel.setPosition(x + (i > 0 ? 50 + width * 2 : 150), y, 0.0);\n            newLabel.setScale(0.75, 0.75, 0.75);\n            newLabel.parent = titleNode;\n            const labelComponent = newLabel.getComponent(Label);\n            labelComponent.string = i ? '----------Composite Mode----------' : '----------Single Mode----------';\n            labelComponent.color = Color.WHITE;\n            labelComponent.overflow = 0;\n            this.labelComponentList[this.labelComponentList.length] = labelComponent;\n        }\n\n        y -= height;\n        // single\n        let currentRow = 0;\n        for (let i = 0; i < this.strSingle.length; i++, currentRow++) {\n            if (i === this.strSingle.length >> 1) {\n                x += width;\n                currentRow = 0;\n            }\n            const newNode = i ? instantiate(this.singleModeToggle) : this.singleModeToggle;\n            newNode.setPosition(x, y - height * currentRow, 0.0);\n            newNode.setScale(0.5, 0.5, 0.5);\n            newNode.parent = this.singleModeToggle.parent;\n\n            const textComponent = newNode.getComponentInChildren(RichText);\n            textComponent.string = this.strSingle[i];\n            this.textComponentList[this.textComponentList.length] = textComponent;\n            this.textContentList[this.textContentList.length] = textComponent.string;\n\n            newNode.on(Toggle.EventType.TOGGLE, this.toggleSingleMode, this);\n\n            this.singleModeToggleList[i] = newNode;\n        }\n\n        x += width;\n        // buttons\n        this.EnableAllCompositeModeButton.setPosition(x + 15, y, 0.0);\n        this.EnableAllCompositeModeButton.setScale(0.5, 0.5, 0.5);\n        this.EnableAllCompositeModeButton.on(Button.EventType.CLICK, this.enableAllCompositeMode, this);\n        this.EnableAllCompositeModeButton.parent = buttonNode;\n        let labelComponent = this.EnableAllCompositeModeButton.getComponentInChildren(Label);\n        this.labelComponentList[this.labelComponentList.length] = labelComponent;\n\n        const changeColorButton = instantiate(this.EnableAllCompositeModeButton);\n        changeColorButton.setPosition(x + 90, y, 0.0);\n        changeColorButton.setScale(0.5, 0.5, 0.5);\n        changeColorButton.on(Button.EventType.CLICK, this.changeTextColor, this);\n        changeColorButton.parent = buttonNode;\n        labelComponent = changeColorButton.getComponentInChildren(Label);\n        labelComponent.string = 'TextColor';\n        this.labelComponentList[this.labelComponentList.length] = labelComponent;\n\n        const HideButton = instantiate(this.EnableAllCompositeModeButton);\n        HideButton.setPosition(x + 200, y, 0.0);\n        HideButton.setScale(0.5, 0.5, 0.5);\n        HideButton.on(Button.EventType.CLICK, this.hideUI, this);\n        HideButton.parent = this.node.parent;\n        labelComponent = HideButton.getComponentInChildren(Label);\n        labelComponent.string = 'Hide UI';\n        this.labelComponentList[this.labelComponentList.length] = labelComponent;\n        this.hideButtonLabel = labelComponent;\n\n        // misc\n        y -= 40;\n        for (let i = 0; i < this.strMisc.length; i++) {\n            const newNode = instantiate(this.compositeModeToggle);\n            newNode.setPosition(x, y - height * i, 0.0);\n            newNode.setScale(0.5, 0.5, 0.5);\n            newNode.parent = miscNode;\n\n            const textComponent = newNode.getComponentInChildren(RichText);\n            textComponent.string = this.strMisc[i];\n            this.textComponentList[this.textComponentList.length] = textComponent;\n            this.textContentList[this.textContentList.length] = textComponent.string;\n\n            const toggleComponent = newNode.getComponent(Toggle);\n            toggleComponent.isChecked = i ? true : false;\n            newNode.on(Toggle.EventType.TOGGLE, i ? this.toggleLightingWithAlbedo : this.toggleCSMColoration, this);\n            this.miscModeToggleList[i] = newNode;\n        }\n\n        // composite\n        y -= 150;\n        for (let i = 0; i < this.strComposite.length; i++) {\n            const newNode = i ? instantiate(this.compositeModeToggle) : this.compositeModeToggle;\n            newNode.setPosition(x, y - height * i, 0.0);\n            newNode.setScale(0.5, 0.5, 0.5);\n            newNode.parent = this.compositeModeToggle.parent;\n\n            const textComponent = newNode.getComponentInChildren(RichText);\n            textComponent.string = this.strComposite[i];\n            this.textComponentList[this.textComponentList.length] = textComponent;\n            this.textContentList[this.textContentList.length] = textComponent.string;\n\n            newNode.on(Toggle.EventType.TOGGLE, this.toggleCompositeMode, this);\n\n            this.compositeModeToggleList[i] = newNode;\n        }\n    }\n\n    isTextMatched(textUI, textDescription) : boolean {\n        let tempText = new String(textUI);\n        const findIndex = tempText.search('>');\n        if (findIndex === -1) {\n            return textUI === textDescription;\n        } else {\n            tempText = tempText.substr(findIndex + 1);\n            tempText = tempText.substr(0, tempText.search('<'));\n            return tempText === textDescription;\n        }\n    }\n    toggleSingleMode(toggle: Toggle) {\n        const debugView = director.root!.debugView;\n        const textComponent = toggle.getComponentInChildren(RichText);\n        for (let i = 0; i < this.strSingle.length; i++) {\n            if (this.isTextMatched(textComponent.string, this.strSingle[i])) {\n                debugView.singleMode = i;\n            }\n        }\n    }\n    toggleCompositeMode(toggle: Toggle) {\n        const debugView = director.root!.debugView;\n        const textComponent = toggle.getComponentInChildren(RichText);\n        for (let i = 0; i < this.strComposite.length; i++) {\n            if (this.isTextMatched(textComponent.string, this.strComposite[i])) {\n                debugView.enableCompositeMode(i, toggle.isChecked);\n            }\n        }\n    }\n    toggleLightingWithAlbedo(toggle: Toggle) {\n        const debugView = director.root!.debugView;\n        debugView.lightingWithAlbedo = toggle.isChecked;\n    }\n    toggleCSMColoration(toggle: Toggle) {\n        const debugView = director.root!.debugView;\n        debugView.csmLayerColoration = toggle.isChecked;\n    }\n    enableAllCompositeMode(button: Button) {\n        const debugView = director.root!.debugView;\n        debugView.enableAllCompositeMode(true);\n        for (let i = 0; i < this.compositeModeToggleList.length; i++) {\n            const toggleComponent = this.compositeModeToggleList[i].getComponent(Toggle);\n            toggleComponent.isChecked = true;\n        }\n\n        let toggleComponent = this.miscModeToggleList[0].getComponent(Toggle);\n        toggleComponent.isChecked = false;\n        debugView.csmLayerColoration = false;\n        toggleComponent = this.miscModeToggleList[1].getComponent(Toggle);\n        toggleComponent.isChecked = true;\n        debugView.lightingWithAlbedo = true;\n    }\n    hideUI(button: Button) {\n        const titleNode = this.node.getChildByName('Titles');\n        const activeValue = !titleNode.active;\n        this.singleModeToggleList[0].parent.active = activeValue;\n        this.miscModeToggleList[0].parent.active = activeValue;\n        this.compositeModeToggleList[0].parent.active = activeValue;\n        this.EnableAllCompositeModeButton.parent.active = activeValue;\n        titleNode.active = activeValue;\n        this.hideButtonLabel.string = activeValue ? 'Hide UI' : 'Show UI';\n    }\n\n    private _currentColorIndex = 0;\n    private strColor: string[] = [\n        '<color=#ffffff>',\n        '<color=#000000>',\n        '<color=#ff0000>',\n        '<color=#00ff00>',\n        '<color=#0000ff>',\n    ];\n    private color: Color[] = [\n        Color.WHITE,\n        Color.BLACK,\n        Color.RED,\n        Color.GREEN,\n        Color.BLUE,\n    ];\n    changeTextColor(button: Button) {\n        this._currentColorIndex++;\n        if (this._currentColorIndex >= this.strColor.length) {\n            this._currentColorIndex = 0;\n        }\n        for (let i = 0; i < this.textComponentList.length; i++) {\n            this.textComponentList[i].string = this.strColor[this._currentColorIndex] + this.textContentList[i] + '</color>';\n        }\n        for (let i = 0; i < this.labelComponentList.length; i++) {\n            this.labelComponentList[i].color = this.color[this._currentColorIndex];\n        }\n    }\n\n    onLoad() {\n    }\n    update(deltaTime: number) {\n    }\n}\n"]}