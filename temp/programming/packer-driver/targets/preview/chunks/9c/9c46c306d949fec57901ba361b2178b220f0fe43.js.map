{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFaderTest.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Label", "SceneFader", "ccclass", "property", "SceneFaderTest", "testScenes", "currentSceneIndex", "start", "testButton", "node", "on", "EventType", "CLICK", "onTestButtonClick", "updateStatusLabel", "length", "nextScene", "console", "log", "loadScene", "statusLabel", "currentScene", "string", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AAC/BC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;AAE9B;AACA;AACA;AACA;;gCAEaO,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACJ,MAAD,C,UAGRI,QAAQ,CAACH,KAAD,C,2BALb,MACaI,cADb,SACoCN,SADpC,CAC8C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAOlCO,UAPkC,GAOX,CAAC,UAAD,EAAa,aAAb,EAA4B,WAA5B,CAPW;AAAA,eAQlCC,iBARkC,GAQN,CARM;AAAA;;AAU1CC,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,IAAhB,CAAqBC,EAArB,CAAwBX,MAAM,CAACY,SAAP,CAAiBC,KAAzC,EAAgD,KAAKC,iBAArD,EAAwE,IAAxE;AACH;;AAED,eAAKC,iBAAL;AACH;;AAEDD,QAAAA,iBAAiB,GAAG;AAChB;AACA,eAAKP,iBAAL,GAAyB,CAAC,KAAKA,iBAAL,GAAyB,CAA1B,IAA+B,KAAKD,UAAL,CAAgBU,MAAxE;AACA,cAAMC,SAAS,GAAG,KAAKX,UAAL,CAAgB,KAAKC,iBAArB,CAAlB;AAEAW,UAAAA,OAAO,CAACC,GAAR,wCAAiDF,SAAjD,EALgB,CAOhB;;AACA;AAAA;AAAA,wCAAWG,SAAX,CAAqBH,SAArB;AACH;;AAEOF,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAKM,WAAT,EAAsB;AAClB,gBAAMC,YAAY,GAAG,KAAKhB,UAAL,CAAgB,KAAKC,iBAArB,CAArB;AACA,iBAAKc,WAAL,CAAiBE,MAAjB,kCAAmCD,YAAnC;AACH;AACJ;;AAEDE,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKf,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,IAAhB,CAAqBe,GAArB,CAAyBzB,MAAM,CAACY,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,iBAAtD,EAAyE,IAAzE;AACH;AACJ;;AAxCyC,O;;;;;iBAErB,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Button, Label } from 'cc';\nimport { SceneFader } from './SceneFader';\nconst { ccclass, property } = _decorator;\n\n/**\n * SceneFader 测试组件\n * 用于测试场景渐变效果是否正常工作\n */\n@ccclass('SceneFaderTest')\nexport class SceneFaderTest extends Component {\n    @property(Button)\n    testButton: Button = null!;\n\n    @property(Label)\n    statusLabel: Label = null!;\n\n    private testScenes: string[] = ['mainmenu', 'LevelSelect', 'gamescene'];\n    private currentSceneIndex: number = 0;\n\n    start() {\n        if (this.testButton) {\n            this.testButton.node.on(Button.EventType.CLICK, this.onTestButtonClick, this);\n        }\n        \n        this.updateStatusLabel();\n    }\n\n    onTestButtonClick() {\n        // 循环切换到下一个场景\n        this.currentSceneIndex = (this.currentSceneIndex + 1) % this.testScenes.length;\n        const nextScene = this.testScenes[this.currentSceneIndex];\n        \n        console.log(`Testing SceneFader: Loading scene ${nextScene}`);\n        \n        // 使用 SceneFader 切换场景\n        SceneFader.loadScene(nextScene);\n    }\n\n    private updateStatusLabel() {\n        if (this.statusLabel) {\n            const currentScene = this.testScenes[this.currentSceneIndex];\n            this.statusLabel.string = `当前场景: ${currentScene}\\n点击按钮测试场景切换`;\n        }\n    }\n\n    onDestroy() {\n        if (this.testButton) {\n            this.testButton.node.off(Button.EventType.CLICK, this.onTestButtonClick, this);\n        }\n    }\n}\n"]}