{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "ToggleContainer", "Sprite", "Color", "Label", "TempData", "<PERSON><PERSON><PERSON><PERSON>", "SceneFader", "ccclass", "property", "SelectManager", "carPrices", "insufficientMoneyTimer", "onLoad", "updateLevelToggles", "updateCarToggles", "setupCarPurchaseButtons", "insufficientMoneyLabel", "node", "active", "<PERSON><PERSON><PERSON><PERSON>", "instance", "console", "log", "levelToggleGroup", "toggleItems", "for<PERSON>ach", "toggle", "levelId", "name", "isUnlocked", "isLevelUnlocked", "interactable", "sprite", "getComponent", "color", "WHITE", "BLACK", "updateLevelGradeDisplay", "levelNode", "gradeText", "getLevelGradeText", "gradeLabel", "getChildByName", "getComponentInChildren", "label", "string", "progress", "getLevelProgress", "colorHex", "getLevelGradeColor", "grade", "hexToColor", "hex", "r", "parseInt", "slice", "g", "b", "unlockedCars", "player<PERSON><PERSON>", "carToggleGroup", "carId", "indexOf", "updateCarPurchaseButton", "start", "startButton", "on", "EventType", "CLICK", "onStartGame", "levelToggle", "find", "t", "isChecked", "carToggle", "selectedLevel", "selectedCar", "loadScene", "carNode", "purchaseButton", "undefined", "buttonLabel", "button", "off", "onPurchaseCar", "price", "warn", "error", "money", "spendMoney", "unlockCar", "savePlayerData", "showInsufficientMoneyMessage", "update", "deltaTime", "getCarPrice", "setCarPrice"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,e,OAAAA,e;AAAyBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AACvEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AACT;OACM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U,GAE9B;;+BAMaY,a,WADZF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACR,eAAD,C,UAGRQ,QAAQ,CAACR,eAAD,C,UAGRQ,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACL,KAAD,C,2BAXb,MACaM,aADb,SACmCX,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAWF;AAEvC;AAbyC,eAcjCY,SAdiC,GAcL;AAChC,qBAAS,CADuB;AACf;AACjB,qBAAS,GAFuB;AAEf;AACjB,qBAAS,IAHuB;AAGf;AACjB,qBAAS,IAJuB;AAIf;AACjB,qBAAS,IALuB,CAKf;;AALe,WAdK;AAAA,eAsBjCC,sBAtBiC,GAsBA,CAtBA;AAAA;;AAsBG;AAE5CC,QAAAA,MAAM,GAAG;AACL,eAAKC,kBAAL;AACA,eAAKC,gBAAL;AACA,eAAKC,uBAAL,GAHK,CAKL;;AACA,cAAI,KAAKC,sBAAT,EAAiC;AAC7B,iBAAKA,sBAAL,CAA4BC,IAA5B,CAAiCC,MAAjC,GAA0C,KAA1C;AACH;AACJ;;AAEDL,QAAAA,kBAAkB,GAAG;AACjB,cAAMM,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AAEA,eAAKC,gBAAL,CAAsBC,WAAtB,CAAkCC,OAAlC,CAA2CC,MAAD,IAAoB;AAC1D,gBAAMC,OAAO,GAAGD,MAAM,CAACT,IAAP,CAAYW,IAA5B;AACA,gBAAMC,UAAU,GAAGV,aAAa,CAACW,eAAd,CAA8BH,OAA9B,CAAnB;AAEAN,YAAAA,OAAO,CAACC,GAAR,mBAAkBK,OAAlB,qCAAqCE,UAArC,EAJ0D,CAM1D;;AACAH,YAAAA,MAAM,CAACK,YAAP,GAAsBF,UAAtB;AACA,gBAAMG,MAAM,GAAGN,MAAM,CAACT,IAAP,CAAYgB,YAAZ,CAAyBhC,MAAzB,CAAf;;AACA,gBAAI+B,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACE,KAAP,GAAeL,UAAU,GAAG3B,KAAK,CAACiC,KAAT,GAAiBjC,KAAK,CAACkC,KAAhD;AACH,aAXyD,CAa1D;;;AACA,iBAAKC,uBAAL,CAA6BX,MAAM,CAACT,IAApC,EAA0CU,OAA1C;AACH,WAfD;AAgBH;AAED;AACJ;AACA;;;AACIU,QAAAA,uBAAuB,CAACC,SAAD,EAAkBX,OAAlB,EAAmC;AACtD,cAAMR,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AACA,cAAMmB,SAAS,GAAGpB,aAAa,CAACqB,iBAAd,CAAgCb,OAAhC,CAAlB,CAFsD,CAItD;;AACA,cAAIc,UAAU,GAAGH,SAAS,CAACI,cAAV,CAAyB,YAAzB,CAAjB;;AACA,cAAI,CAACD,UAAL,EAAiB;AAAA;;AACb;AACAA,YAAAA,UAAU,4BAAGH,SAAS,CAACK,sBAAV,CAAiCxC,KAAjC,CAAH,qBAAG,sBAAyCc,IAAtD;AACH;;AAED,cAAIwB,UAAJ,EAAgB;AACZ,gBAAMG,KAAK,GAAGH,UAAU,CAACR,YAAX,CAAwB9B,KAAxB,CAAd;;AACA,gBAAIyC,KAAJ,EAAW;AACP,kBAAIL,SAAJ,EAAe;AACXK,gBAAAA,KAAK,CAACC,MAAN,GAAeN,SAAf;AACAK,gBAAAA,KAAK,CAAC3B,IAAN,CAAWC,MAAX,GAAoB,IAApB,CAFW,CAIX;;AACA,oBAAM4B,QAAQ,GAAG3B,aAAa,CAAC4B,gBAAd,CAA+BpB,OAA/B,CAAjB;;AACA,oBAAImB,QAAJ,EAAc;AACV,sBAAME,QAAQ,GAAG7B,aAAa,CAAC8B,kBAAd,CAAiCH,QAAQ,CAACI,KAA1C,CAAjB;AACAN,kBAAAA,KAAK,CAACV,KAAN,GAAc,KAAKiB,UAAL,CAAgBH,QAAhB,CAAd;AACH;AACJ,eAVD,MAUO;AACHJ,gBAAAA,KAAK,CAACC,MAAN,GAAe,EAAf;AACAD,gBAAAA,KAAK,CAAC3B,IAAN,CAAWC,MAAX,GAAoB,KAApB;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACYiC,QAAAA,UAAU,CAACC,GAAD,EAAqB;AACnC,cAAMC,CAAC,GAAGC,QAAQ,CAACF,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;AACA,cAAMC,CAAC,GAAGF,QAAQ,CAACF,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;AACA,cAAME,CAAC,GAAGH,QAAQ,CAACF,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;AACA,iBAAO,IAAIrD,KAAJ,CAAUmD,CAAV,EAAaG,CAAb,EAAgBC,CAAhB,EAAmB,GAAnB,CAAP;AACH;;AAED3C,QAAAA,gBAAgB,GAAG;AACf,cAAM4C,YAAY,GAAG;AAAA;AAAA,8CAActC,QAAd,CAAuBuC,UAAvB,CAAkCD,YAAvD;AACA,eAAKE,cAAL,CAAoBpC,WAApB,CAAgCC,OAAhC,CAAyCC,MAAD,IAAoB;AACxD,gBAAMmC,KAAK,GAAGnC,MAAM,CAACT,IAAP,CAAYW,IAA1B;AACA,gBAAMC,UAAU,GAAG6B,YAAY,CAACI,OAAb,CAAqBD,KAArB,MAAgC,CAAC,CAApD,CAFwD,CAIxD;;AACAnC,YAAAA,MAAM,CAACK,YAAP,GAAsBF,UAAtB;AACA,gBAAMG,MAAM,GAAGN,MAAM,CAACT,IAAP,CAAYgB,YAAZ,CAAyBhC,MAAzB,CAAf;;AACA,gBAAI+B,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACE,KAAP,GAAeL,UAAU,GAAG3B,KAAK,CAACiC,KAAT,GAAiBjC,KAAK,CAACkC,KAAhD;AACH,aATuD,CAWxD;;;AACA,iBAAK2B,uBAAL,CAA6BrC,MAAM,CAACT,IAApC,EAA0C4C,KAA1C,EAAiDhC,UAAjD;AACH,WAbD;AAcH;;AAEDmC,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBhD,IAAjB,CAAsBiD,EAAtB,CAAyBnE,MAAM,CAACoE,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,WAAtD,EAAmE,IAAnE;AACH;AACJ;;AAEDA,QAAAA,WAAW,GAAG;AACV;AACA,cAAMC,WAAW,GAAG,KAAK/C,gBAAL,CAAsBC,WAAtB,CAAkC+C,IAAlC,CAAwCC,CAAD,IAAYA,CAAC,CAACC,SAArD,CAApB,CAFU,CAGV;;AACA,cAAMC,SAAS,GAAG,KAAKd,cAAL,CAAoBpC,WAApB,CAAgC+C,IAAhC,CAAsCC,CAAD,IAAYA,CAAC,CAACC,SAAnD,CAAlB;;AAEA,cAAI,CAACH,WAAD,IAAgB,CAACI,SAArB,EAAgC;AAC5B;AACA;AACH,WATS,CAWV;;;AACA;AAAA;AAAA,oCAASC,aAAT,GAAyBL,WAAW,CAACrD,IAAZ,CAAiBW,IAA1C;AACA;AAAA;AAAA,oCAASgD,WAAT,GAAuBF,SAAS,CAACzD,IAAV,CAAeW,IAAtC;AAEAP,UAAAA,OAAO,CAACC,GAAR,CAAYgD,WAAW,CAACrD,IAAZ,CAAiBW,IAA7B,EAAkC8C,SAAS,CAACzD,IAAV,CAAeW,IAAjD,EAfU,CAiBV;;AACA;AAAA;AAAA,wCAAWiD,SAAX,CAAqB,WAArB;AACH;AAID;AACJ;AACA;;;AACI9D,QAAAA,uBAAuB,GAAG;AACtB,eAAK6C,cAAL,CAAoBpC,WAApB,CAAgCC,OAAhC,CAAyCC,MAAD,IAAoB;AACxD,gBAAMmC,KAAK,GAAGnC,MAAM,CAACT,IAAP,CAAYW,IAA1B;AACA,gBAAMC,UAAU,GAAG;AAAA;AAAA,gDAAcT,QAAd,CAAuBuC,UAAvB,CAAkCD,YAAlC,CAA+CI,OAA/C,CAAuDD,KAAvD,MAAkE,CAAC,CAAtF;AACA,iBAAKE,uBAAL,CAA6BrC,MAAM,CAACT,IAApC,EAA0C4C,KAA1C,EAAiDhC,UAAjD;AACH,WAJD;AAKH;AAED;AACJ;AACA;;;AACIkC,QAAAA,uBAAuB,CAACe,OAAD,EAAgBjB,KAAhB,EAA+BhC,UAA/B,EAAoD;AACvE;AACA,cAAIkD,cAAc,GAAGD,OAAO,CAACpC,cAAR,CAAuB,gBAAvB,CAArB;;AAEA,cAAI,CAACb,UAAD,IAAe,KAAKnB,SAAL,CAAemD,KAAf,MAA0BmB,SAA7C,EAAwD;AACpD;AACA,gBAAI,CAACD,cAAL,EAAqB;AACjB;AACAA,cAAAA,cAAc,GAAGD,OAAO,CAACpC,cAAR,CAAuB,gBAAvB,CAAjB;AACH;;AAED,gBAAIqC,cAAJ,EAAoB;AAAA;;AAChBA,cAAAA,cAAc,CAAC7D,MAAf,GAAwB,IAAxB,CADgB,CAGhB;;AACA,kBAAM+D,WAAW,4BAAGF,cAAc,CAACrC,cAAf,CAA8B,OAA9B,CAAH,qBAAG,sBAAwCT,YAAxC,CAAqD9B,KAArD,CAApB;;AACA,kBAAI8E,WAAJ,EAAiB;AACbA,gBAAAA,WAAW,CAACpC,MAAZ,qBAA2B,KAAKnC,SAAL,CAAemD,KAAf,CAA3B;AACH,eAPe,CAShB;;;AACA,kBAAMqB,MAAM,GAAGH,cAAc,CAAC9C,YAAf,CAA4BlC,MAA5B,CAAf;;AACA,kBAAImF,MAAJ,EAAY;AACRA,gBAAAA,MAAM,CAACjE,IAAP,CAAYkE,GAAZ,CAAgBpF,MAAM,CAACoE,SAAP,CAAiBC,KAAjC;AACAc,gBAAAA,MAAM,CAACjE,IAAP,CAAYiD,EAAZ,CAAenE,MAAM,CAACoE,SAAP,CAAiBC,KAAhC,EAAuC,MAAM;AACzC,uBAAKgB,aAAL,CAAmBvB,KAAnB;AACH,iBAFD,EAEG,IAFH;AAGH;AACJ;AACJ,WAzBD,MAyBO;AACH;AACA,gBAAIkB,cAAJ,EAAoB;AAChBA,cAAAA,cAAc,CAAC7D,MAAf,GAAwB,KAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIkE,QAAAA,aAAa,CAACvB,KAAD,EAAgB;AACzB,cAAMwB,KAAK,GAAG,KAAK3E,SAAL,CAAemD,KAAf,CAAd;;AACA,cAAIwB,KAAK,KAAKL,SAAd,EAAyB;AACrB3D,YAAAA,OAAO,CAACiE,IAAR,mBAAmBzB,KAAnB;AACA;AACH;;AAED,cAAM1C,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;;AACA,cAAI,CAACD,aAAL,EAAoB;AAChBE,YAAAA,OAAO,CAACkE,KAAR,CAAc,qBAAd;AACA;AACH,WAXwB,CAazB;;;AACA,cAAIpE,aAAa,CAACwC,UAAd,CAAyB6B,KAAzB,IAAkCH,KAAtC,EAA6C;AACzC;AACA,gBAAIlE,aAAa,CAACsE,UAAd,CAAyBJ,KAAzB,CAAJ,EAAqC;AACjClE,cAAAA,aAAa,CAACuE,SAAd,CAAwB7B,KAAxB;AAEAxC,cAAAA,OAAO,CAACC,GAAR,2CAAsBuC,KAAtB,2BAAkCwB,KAAlC,oBAHiC,CAKjC;;AACA,mBAAKvE,gBAAL,GANiC,CAQjC;;AACAK,cAAAA,aAAa,CAACwE,cAAd;AACH;AACJ,WAbD,MAaO;AACH;AACA,iBAAKC,4BAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACIA,QAAAA,4BAA4B,GAAG;AAC3B,cAAI,KAAK5E,sBAAT,EAAiC;AAC7B,iBAAKA,sBAAL,CAA4B6B,MAA5B,GAAqC,OAArC;AACA,iBAAK7B,sBAAL,CAA4BC,IAA5B,CAAiCC,MAAjC,GAA0C,IAA1C;AACA,iBAAKP,sBAAL,GAA8B,GAA9B,CAH6B,CAGM;AACtC;AACJ;AAED;AACJ;AACA;;;AACIkF,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAKnF,sBAAL,GAA8B,CAAlC,EAAqC;AACjC,iBAAKA,sBAAL,IAA+BmF,SAA/B;;AACA,gBAAI,KAAKnF,sBAAL,IAA+B,CAAnC,EAAsC;AAClC,kBAAI,KAAKK,sBAAT,EAAiC;AAC7B,qBAAKA,sBAAL,CAA4BC,IAA5B,CAAiCC,MAAjC,GAA0C,KAA1C;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACI6E,QAAAA,WAAW,CAAClC,KAAD,EAAwB;AAC/B,iBAAO,KAAKnD,SAAL,CAAemD,KAAf,KAAyB,CAAhC;AACH;AAED;AACJ;AACA;;;AACImC,QAAAA,WAAW,CAACnC,KAAD,EAAgBwB,KAAhB,EAA+B;AACtC,eAAK3E,SAAL,CAAemD,KAAf,IAAwBwB,KAAxB;AACH;;AAjRwC,O;;;;;iBAEL,I;;;;;;;iBAGF,I;;;;;;;iBAGZ,I;;;;;;;iBAGU,I", "sourcesContent": ["import { _decorator, Component, Button, ToggleContainer, Toggle, Sprite, Color, Label, Node } from 'cc';\nimport { TempData } from './TempData';\nimport { PlayerManager } from './PlayerManager';\nimport { SceneFader } from './SceneFader';\n// @ts-ignore\nconst { ccclass, property } = _decorator;\n\n// 车辆价格配置\ninterface CarPriceConfig {\n    [carId: string]: number;\n}\n\n@ccclass('SelectManager')\nexport class SelectManager extends Component {\n    @property(ToggleContainer)\n    levelToggleGroup: ToggleContainer = null!;\n\n    @property(ToggleContainer)\n    carToggleGroup: ToggleContainer = null!;\n\n    @property(Button)\n    startButton: Button = null!;\n\n    @property(Label)\n    insufficientMoneyLabel: Label = null!; // 金币不足提示标签\n\n    // 车辆价格配置\n    private carPrices: CarPriceConfig = {\n        'car-1': 0,      // 默认车辆免费\n        'car-2': 500,    // 第二辆车500金币\n        'car-3': 1000,   // 第三辆车1000金币\n        'car-4': 1500,   // 第四辆车1500金币\n        'car-5': 2000,   // 第五辆车2000金币\n    };\n\n    private insufficientMoneyTimer: number = 0; // 金币不足提示计时器\n\n    onLoad() {\n        this.updateLevelToggles();\n        this.updateCarToggles();\n        this.setupCarPurchaseButtons();\n\n        // 隐藏金币不足提示\n        if (this.insufficientMoneyLabel) {\n            this.insufficientMoneyLabel.node.active = false;\n        }\n    }\n\n    updateLevelToggles() {\n        const playerManager = PlayerManager.instance;\n        console.log('更新关卡显示');\n\n        this.levelToggleGroup.toggleItems.forEach((toggle: Toggle) => {\n            const levelId = toggle.node.name;\n            const isUnlocked = playerManager.isLevelUnlocked(levelId);\n\n            console.log(`关卡 ${levelId}: 解锁状态 = ${isUnlocked}`);\n\n            // 设置交互性和颜色\n            toggle.interactable = isUnlocked;\n            const sprite = toggle.node.getComponent(Sprite);\n            if (sprite) {\n                sprite.color = isUnlocked ? Color.WHITE : Color.BLACK;\n            }\n\n            // 更新评级显示\n            this.updateLevelGradeDisplay(toggle.node, levelId);\n        });\n    }\n\n    /**\n     * 更新关卡评级显示\n     */\n    updateLevelGradeDisplay(levelNode: Node, levelId: string) {\n        const playerManager = PlayerManager.instance;\n        const gradeText = playerManager.getLevelGradeText(levelId);\n\n        // 查找或创建评级标签\n        let gradeLabel = levelNode.getChildByName('GradeLabel');\n        if (!gradeLabel) {\n            // 如果没有评级标签节点，尝试查找现有的Label子节点\n            gradeLabel = levelNode.getComponentInChildren(Label)?.node;\n        }\n\n        if (gradeLabel) {\n            const label = gradeLabel.getComponent(Label);\n            if (label) {\n                if (gradeText) {\n                    label.string = gradeText;\n                    label.node.active = true;\n\n                    // 设置评级颜色\n                    const progress = playerManager.getLevelProgress(levelId);\n                    if (progress) {\n                        const colorHex = playerManager.getLevelGradeColor(progress.grade);\n                        label.color = this.hexToColor(colorHex);\n                    }\n                } else {\n                    label.string = '';\n                    label.node.active = false;\n                }\n            }\n        }\n    }\n\n    /**\n     * 将十六进制颜色转换为Cocos Color\n     */\n    private hexToColor(hex: string): Color {\n        const r = parseInt(hex.slice(1, 3), 16);\n        const g = parseInt(hex.slice(3, 5), 16);\n        const b = parseInt(hex.slice(5, 7), 16);\n        return new Color(r, g, b, 255);\n    }\n\n    updateCarToggles() {\n        const unlockedCars = PlayerManager.instance.playerData.unlockedCars;\n        this.carToggleGroup.toggleItems.forEach((toggle: Toggle) => {\n            const carId = toggle.node.name;\n            const isUnlocked = unlockedCars.indexOf(carId) !== -1;\n\n            // 设置车辆图标的交互性和颜色\n            toggle.interactable = isUnlocked;\n            const sprite = toggle.node.getComponent(Sprite);\n            if (sprite) {\n                sprite.color = isUnlocked ? Color.WHITE : Color.BLACK;\n            }\n\n            // 处理购买按钮的显示\n            this.updateCarPurchaseButton(toggle.node, carId, isUnlocked);\n        });\n    }\n\n    start() {\n        if (this.startButton) {\n            this.startButton.node.on(Button.EventType.CLICK, this.onStartGame, this);\n        }\n    }\n\n    onStartGame() {\n        // 获取当前选中的level\n        const levelToggle = this.levelToggleGroup.toggleItems.find((t: any) => t.isChecked);\n        // 获取当前选中的car\n        const carToggle = this.carToggleGroup.toggleItems.find((t: any) => t.isChecked);\n\n        if (!levelToggle || !carToggle) {\n            // 你可以在这里弹窗提示\"请选择关卡和车辆\"\n            return;\n        }\n\n        // 记录选择到TempData\n        TempData.selectedLevel = levelToggle.node.name;\n        TempData.selectedCar = carToggle.node.name;\n\n        console.log(levelToggle.node.name,carToggle.node.name)\n\n        // 使用渐变效果切换到游戏场景\n        SceneFader.loadScene('gamescene');\n    }\n\n\n\n    /**\n     * 设置车辆购买按钮\n     */\n    setupCarPurchaseButtons() {\n        this.carToggleGroup.toggleItems.forEach((toggle: Toggle) => {\n            const carId = toggle.node.name;\n            const isUnlocked = PlayerManager.instance.playerData.unlockedCars.indexOf(carId) !== -1;\n            this.updateCarPurchaseButton(toggle.node, carId, isUnlocked);\n        });\n    }\n\n    /**\n     * 更新单个车辆的购买按钮\n     */\n    updateCarPurchaseButton(carNode: Node, carId: string, isUnlocked: boolean) {\n        // 查找或创建购买按钮\n        let purchaseButton = carNode.getChildByName('PurchaseButton');\n\n        if (!isUnlocked && this.carPrices[carId] !== undefined) {\n            // 车辆未解锁且有价格配置，显示购买按钮\n            if (!purchaseButton) {\n                // 创建购买按钮（这里假设场景中已经有购买按钮节点）\n                purchaseButton = carNode.getChildByName('PurchaseButton');\n            }\n\n            if (purchaseButton) {\n                purchaseButton.active = true;\n\n                // 设置按钮文本\n                const buttonLabel = purchaseButton.getChildByName('Label')?.getComponent(Label);\n                if (buttonLabel) {\n                    buttonLabel.string = `购买 ${this.carPrices[carId]}`;\n                }\n\n                // 绑定点击事件\n                const button = purchaseButton.getComponent(Button);\n                if (button) {\n                    button.node.off(Button.EventType.CLICK);\n                    button.node.on(Button.EventType.CLICK, () => {\n                        this.onPurchaseCar(carId);\n                    }, this);\n                }\n            }\n        } else {\n            // 车辆已解锁或免费，隐藏购买按钮\n            if (purchaseButton) {\n                purchaseButton.active = false;\n            }\n        }\n    }\n\n    /**\n     * 购买车辆\n     */\n    onPurchaseCar(carId: string) {\n        const price = this.carPrices[carId];\n        if (price === undefined) {\n            console.warn(`车辆 ${carId} 没有配置价格`);\n            return;\n        }\n\n        const playerManager = PlayerManager.instance;\n        if (!playerManager) {\n            console.error('PlayerManager 实例不存在');\n            return;\n        }\n\n        // 检查玩家金币是否足够\n        if (playerManager.playerData.money >= price) {\n            // 扣除金币并解锁车辆\n            if (playerManager.spendMoney(price)) {\n                playerManager.unlockCar(carId);\n\n                console.log(`成功购买车辆 ${carId}，花费 ${price} 金币`);\n\n                // 更新UI显示\n                this.updateCarToggles();\n\n                // 保存数据\n                playerManager.savePlayerData();\n            }\n        } else {\n            // 金币不足，显示提示\n            this.showInsufficientMoneyMessage();\n        }\n    }\n\n    /**\n     * 显示金币不足提示\n     */\n    showInsufficientMoneyMessage() {\n        if (this.insufficientMoneyLabel) {\n            this.insufficientMoneyLabel.string = '金币不足！';\n            this.insufficientMoneyLabel.node.active = true;\n            this.insufficientMoneyTimer = 3.0; // 3秒后隐藏\n        }\n    }\n\n    /**\n     * 更新方法，处理金币不足提示的计时\n     */\n    update(deltaTime: number) {\n        if (this.insufficientMoneyTimer > 0) {\n            this.insufficientMoneyTimer -= deltaTime;\n            if (this.insufficientMoneyTimer <= 0) {\n                if (this.insufficientMoneyLabel) {\n                    this.insufficientMoneyLabel.node.active = false;\n                }\n            }\n        }\n    }\n\n    /**\n     * 获取车辆价格\n     */\n    getCarPrice(carId: string): number {\n        return this.carPrices[carId] || 0;\n    }\n\n    /**\n     * 设置车辆价格\n     */\n    setCarPrice(carId: string, price: number) {\n        this.carPrices[carId] = price;\n    }\n}"]}